import '../../flavors.dart';

/// Utility class for managing flavor-specific assets
/// Provides methods to get the correct asset path based on current flavor
class FlavorAssets {
  static String getIcon(String iconName) => switch (F.appFlavor) {
        Flavor.sis => 'assets/svg/sis/$iconName',
        Flavor.cfroex || Flavor.ncm || Flavor.sf_app => 'assets/svg/$iconName',
      };

  /// Get flavor-specific image path with fallback to default
  static String getImage(String imageName) => switch (F.appFlavor) {
        Flavor.sis => 'assets/images/sis/$imageName',
        Flavor.cfroex ||
        Flavor.ncm ||
        Flavor.sf_app =>
          'assets/images/$imageName',
      };

  /// Get flavor-specific PNG icon path with fallback to default
  static String getPngIcon(String iconName) => switch (F.appFlavor) {
        Flavor.sis => 'assets/icons/sis/$iconName',
        Flavor.cfroex ||
        Flavor.ncm ||
        Flavor.sf_app =>
          'assets/icons/$iconName',
      };
}
