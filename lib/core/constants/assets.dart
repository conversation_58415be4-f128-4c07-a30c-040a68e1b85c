import '../config/app_config.dart';
import '../utils/flavor_assets.dart';

class Assets {
  // Logos & Branding
  static String logoSvg = AppConfig.instance.icon;
  static String introVideo = AppConfig.instance.introVideo;

  // Navigation Icons
  static String iconsHome = FlavorAssets.getIcon('home.svg');
  static String iconsCommunity = FlavorAssets.getIcon('community.svg');
  static String iconsProfile = FlavorAssets.getIcon('profile.svg');
  static String iconsSmartBuy = FlavorAssets.getIcon('buy.svg');
  static String iconsQuote = FlavorAssets.getIcon('quote.svg');
  static String iconsHistory = FlavorAssets.getIcon('history.svg');

  // Action & UI Icons
  static const String iconsObscure = 'assets/svg/eye-on.svg';
  static const String iconsObscureHide = 'assets/svg/eye-off.svg';
  static const String copy = 'assets/svg/copy.svg';
  static const String emailIcon = 'assets/svg/email.svg';
  static const String chart = 'assets/svg/chart.svg';
  static const String alertSuccess = 'assets/svg/success.svg';
  static const String alertError = 'assets/svg/error.svg';
  static const String history = 'assets/svg/history2.svg';
  static const String messages = 'assets/svg/messages.svg';
  static const String messagesRead = 'assets/svg/message-read.svg';
  static const String support = 'assets/svg/support.svg';
  static const String profile = 'assets/svg/profile2.svg';
  static const String profile1 = 'assets/svg/profile3.svg';
  static const String deposit = 'assets/svg/deposit.svg';
  static const String withdraw = 'assets/svg/withdraw2.svg';
  static const String transfer = 'assets/svg/transfer.svg';
  static const String records = 'assets/svg/record.svg';
  static const String arrowForward = 'assets/svg/arrow_forward.svg';
  static const String arrowDown = 'assets/svg/arrow_drop_down.svg';
  static const String arrowLeft = 'assets/svg/arrow-left.svg';
  static const String titleArrow = 'assets/svg/title-arrow.svg';
  static const String pin = 'assets/svg/pin.svg';
  static const String empty = 'assets/svg/empty.svg';
  static const String loader = 'assets/svg/loader.svg';
  static const String checkMark = 'assets/svg/checkMark.svg';
  static const String refresh = 'assets/svg/refresh.svg';
  static const String circles = 'assets/svg/circles-group.svg';
  static const String marketClose = 'assets/svg/market_close.svg';
  static const String marketDown = 'assets/svg/market_down.svg';
  static const String marketUp = 'assets/svg/market_up.svg';
  static const String sortUp = 'assets/svg/sort_up.svg';
  static const String sortDown = 'assets/svg/sort_down.svg';
  static const String iconNotif = 'assets/svg/iconNotif.svg';
  static const String iconSupport = 'assets/svg/iconSupport.svg';
  static const String tradingIcon = 'assets/svg/trading_icon.svg';
  static const String fundingIcon = 'assets/svg/funding_icon.svg';
  static const String verified = 'assets/svg/verified.svg';
  static const String contracts1 = 'assets/svg/contracts_1.svg';
  static const String chatIcon = 'assets/svg/chat.svg';
  static const String member = 'assets/svg/member.svg';
  static const String members = 'assets/svg/members.svg';
  static const String other = 'assets/svg/other.svg';
  static const String termsAndConditionIcon = 'assets/svg/terms&condition_icons.svg';

  // Network / Protocol Icons
  static const String trcIcon = 'assets/svg/trc.svg';
  static const String ercIcon = 'assets/svg/erc.svg';

  // Crypto Icons
  static const String iconAvax = 'assets/svg/avax.svg';
  static const String iconBnb = 'assets/svg/bnb.svg';
  static const String iconBtc = 'assets/svg/btc.svg';
  static const String iconEth = 'assets/svg/eth.svg';

  static const String btc = 'assets/svg/crypto/btc.svg';
  static const String eth = 'assets/svg/crypto/eth.svg';
  static const String bnb = 'assets/svg/crypto/bnb.svg';
  static const String matic = 'assets/svg/crypto/matic.svg';
  static const String atom = 'assets/svg/crypto/atom.svg';
  static const String avax = 'assets/svg/crypto/avax.svg';
  static const String near = 'assets/svg/crypto/near.svg';

  // Avatars & Chat
  static const String userIcon = 'assets/svg/user-icon.svg';
  static const String avatarChat = 'assets/svg/avatar_chat.svg';
  static const String userEmptyAvatar = 'assets/svg/user_empty_avatar.svg';
  static const String groupEmptyAvatar = 'assets/svg/group_empty_avatar.svg';
  static const String groupIcon = 'assets/svg/group_icon.svg';
  static const String emptySearch = 'assets/svg/search_empty.svg';

  static  String iconsLogin =  FlavorAssets.getIcon('login.svg');
  static  String iconsSignup =  FlavorAssets.getIcon('signup.svg');
  // Images
  static const String iconsFlag = 'assets/icons/flag.png';
  static const String qrCode = 'assets/images/image_sample_QR.png';
  // static const String gradChat = 'assets/images/grad_chat.png';
  // static const String gradContacts = 'assets/images/grad_contacts.png';
  // static const String customerSupport = 'assets/images/customer_support.png';

  static const String level0 = 'assets/icons/level0.png';
  static const String level1 = 'assets/icons/level1.png';
  static const String level2 = 'assets/icons/level2.png';
  static const String level3 = 'assets/icons/level3.png';
  static const String level4 = 'assets/icons/level4.png';
  static const String level5 = 'assets/icons/level5.png';

  // HTML Files
  static const String termsAndCondition = 'assets/html/terms_and_condition.html';
  static const String privacyPolicy = 'assets/html/privacy.html';
  static const String terms = 'assets/html/terms.html';
  static const String privacy = 'assets/html/privacy.html';

}
