import 'package:flutter/material.dart';
import '../../flavors.dart';
import 'color_pallette.dart';

/// Flavor-specific theme configurations
/// Provides different color schemes, spacing, and styling based on app flavor
class FlavorTheme {
  /// Get flavor-specific primary color
  static Color getBottomNavColor() {
    switch (F.appFlavor) {
      case Flavor.sis:
        return ColorPalette.bottomNavColor;
      case Flavor.cfroex:
      case Flavor.ncm:
      case Flavor.sf_app:
        return ColorPalette.primaryColor; // Default
    }
  }
}
