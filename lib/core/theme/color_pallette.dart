import 'package:flutter/material.dart';

class ColorPalette {
  static const mainColor = Color.fromARGB(255, 0, 46, 110);

//Dark Theme

  static Color get primaryColor => mainColor;
  static Color get primaryColorDark => const Color(0xFF1C6FE6);

  static Color get primaryVar1 => mainColor;
  static Color get primaryVar1Dark => const Color(0xFF1C6FE6);

  static Color get primaryVar2 => mainColor;
  static Color get primaryVar2Dark => const Color(0xFF1C6FE6);

  static Color get primaryVar3 => mainColor;
  static Color get primaryVar3Dark => const Color(0xFF1C6FE6);

  static Color get textFieldBorderColor => mainColor;
  static Color get textFieldBorderColorDark => const Color(0xFF1C6FE6);

  static Color get feeColor => mainColor;
  static Color get feeColorDark => const Color(0xFF1A1A1A);

  static Color get iconBackgroundColor => mainColor;
  static Color get iconBackgroundColorDark => const Color(0xFF1A1A1A);

  static Color get iconBackgroundColor2 => mainColor;
  static Color get iconBackgroundColor2Dark => const Color(0xFF1C6FE6);

  static Color get primaryVar4 => mainColor;
  static Color get primaryVar4Dark => const Color(0xFF1C6FE6);

  static Color get iconColor => mainColor;
  static Color get iconColorDark => const Color(0xFF1A1A1A);

  static Color get shimmerColor => const Color(0xFFF3F6FE);
  static Color get shimmerColorDark => const Color(0xFF1A1A1A);

  static Color get shadowColor => const Color(0xFFF3F6FE);
  static Color get shadowColorDark => const Color(0xFF3A3F4E);

  static Color get secondaryColor => const Color.fromARGB(255, 228, 228, 228);
  static Color get secondaryColorDark => const Color.fromARGB(255, 128, 128, 128);

  static Color get backgroundColor => const Color(0xFFF9F9FA);
  static Color get backgroundColorDark => const Color(0xFF1A1A1A);

  static Color get white => const Color(0xFFFFFFFF);
  static Color get whiteDark => const Color(0xFFE1E1E1);

  static Color get secondaryVar1 => const Color(0xFFADADBD);
  static Color get secondaryVar1Dark => const Color(0xFF5D5D6D);

  static Color get primaryBlack => const Color(0xFF333333);
  static Color get primaryBlackDark => const Color(0xFF000000);

  static Color get successColor => const Color(0xFF3DBD86);
  static Color get successColorDark => const Color(0xFF26794F);

  static Color get greyColor1 => const Color(0xFFEEEFF0);
  static Color get greyColor1Dark => const Color(0xFF232323);

  static Color get greyColor2 => const Color(0xFFD7D7D7);
  static Color get greyColor2Dark => const Color(0xFF3A3A3A);

  static Color get greyColor3 => const Color(0xFFADADBD);
  static Color get greyColor3Dark => const Color(0xFF6D6D7D);

  static Color get greyColor4 => const Color(0xFF83878C);
  static Color get greyColor4Dark => const Color(0xFFE0E0E0);

  static Color get greyColor5 => const Color(0xFFEEEEEE);
  static Color get greyColor5Dark => const Color(0xFF4A4A4A);

  static Color get greyColor6 => const Color(0xFFF9F9FA);
  static Color get greyColor6Dark => const Color(0xFF2A2A2A);

  static Color get lightGrey => const Color(0xFFEEEFF0);
  static Color get lightGreyDark => const Color(0xFF3A3A3A);

  static Color get lightGrey4 => const Color(0xFFD9DBE1);
  static Color get lightGrey4Dark => const Color(0xFF2B2B2B);

  static Color get trc => const Color(0xFF26A17B);
  static Color get trcDark => const Color(0xFF175643);

  static Color get lightGrey3 => const Color(0xFFF9F9F9);
  static Color get lightGrey3Dark => const Color(0xFF2A2A2A);

  static Color get lightGrey2 => const Color(0xFFADADBD);
  static Color get lightGrey2Dark => const Color(0xFF5D5D6D);

  static Color get lightGrey6 => const Color(0xFFD9D9D9);

  static Color get black => const Color(0xFF000000);
  static Color get blackDark => const Color(0xFF0A0A0A);

  static Color get labelColor => const Color(0xFFADADBD);
  static Color get labelColorDark => const Color(0xFF5D5D6D);

  static Color get titleColor => const Color(0xFF333333);
  static Color get titleColorDark => const Color(0xFFC2C2C2);

  static Color get dropShadow => const Color(0xff000029);
  static Color get dropShadowDark => const Color(0xff000014);

  static Color get skeletonColor => const Color(0xFFECECEC);
  static Color get skeletonColorDark => const Color(0xFF2E2E2E);

  static Color get borderColor => const Color(0xFFD3D3DC);
  static Color get borderColorDark => const Color(0xFF5B5B64);

  static Color get borderColor2 => const Color(0xFF707070);
  static Color get borderColor2Dark => const Color(0xFF3A3A3A);

  static Color get deniedColor => const Color(0xFFEF481D);
  static Color get deniedColorDark => const Color(0xFF8C2410);

  static Color get subTitleColor => const Color(0xFF4A4A4A);
  static Color get subTitleColorDark => const Color(0xFF83878C);

  static Color get subTitleColor2 => const Color(0xFF666666);
  static Color get subTitleColor2Dark => const Color(0xFF83878C);

  static Color get pendingColor => const Color(0xFFFEAC40);
  static Color get pendingColorDark => const Color(0xFFB9732A);

  static Color get greenColor => const Color(0xFF56B117);
  static Color get greenColorDark => const Color(0xFF2D6E0F);

  static Color get backgroundColor1 => const Color(0xFFF5F5F5);
  static Color get backgroundColor1Dark => const Color(0xFF1A1A1A);

  static Color get tagBlue => const Color(0xFF5688D0);
  static Color get tagBlueDark => const Color(0xFF2E4B6F);

  static Color get tagGreen => const Color(0xFF56D070);
  static Color get tagGreenDark => const Color(0xFF2F703E);

  static Color get tagRed => const Color(0xFFD05D56);
  static Color get tagRedDark => const Color(0xFF8C2F2A);

  static Color get cardColor => const Color(0xFFFFFFFF);
  static Color get cardColorDark => const Color(0xFF2C2C2C);

  static Color get viewAllColor => mainColor;
  static Color get viewAllColorDark => const Color(0xFF848484);

  static Color get appBarIconColor => const Color(0xFF1A1A1A);
  static Color get appBarIconColorDark => const Color(0xFFE1E1E1);

  static Color get tableHeaderColor => const Color(0xFFF1F4FF);
  static Color get tableHeaderColorDark => const Color(0xFF232323);

  static Color get buttonColorDisabled => const Color(0xFF83878C);
  static Color get buttonColorDisabledDark => const Color(0xFF232323);

  static Color get borderColor3 => const Color(0xFFE6E6E6);
  static Color get borderColor3Dark => const Color(0xFF232323);

  static Color get backgroundColor2 => const Color(0xFFF8F9FF);
  static Color get backgroundColor2Dark => const Color(0xFF232323);

  static Color get chatSubtitle => const Color(0xff8D8E90);
  static Color get chatSubtitleDark => const Color(0xff8D8E90);

  static Color get chatDividerColor => const Color(0xffD9D9D9);
  static Color get chatDividerColorDark => const Color(0xFF2C2C2C);

  static Color get cardColor2 => const Color(0xFFF8F9FA);
  static Color get cardColor2Dark => const Color(0xFF2C2C2C);

  static Color get cardColor3 => const Color(0xFFEEEFF0);
  static Color get cardColor3Dark => const Color(0xFF232323);

  static Color get cardColor4 => const Color(0xfff3f6fe);
  static Color get cardColor4Dark => const Color(0xFF2C2C2C);

  static Color get bottomNavColor => const Color(0xFF1C6FE6);
  static Color get bottomNavColorDark => const Color(0xFF1C6FE6);

  static String get hexColor1 => '#26A17B';

  static const MaterialColor materialPrimary = MaterialColor(
    0xFF7494F4,
    <int, Color>{
      50: Color(0xFF063b87),
      100: Color(0xFF063b87),
      200: Color(0xFF063b87),
      300: Color(0xFF063b87),
      400: Color(0xFF063b87),
      500: Color(0xFF063b87),
      600: Color(0xFF063b87),
      700: Color(0xFF063b87),
      800: Color(0xFF063b87),
      900: Color(0xFF063b87),
    },
  );
}

class HexColor extends Color {
  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));

  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return int.parse(hexColor, radix: 16);
  }
}
