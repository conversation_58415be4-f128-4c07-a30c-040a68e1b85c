import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../constants/enums.dart';
import 'common_shimmer.dart';

/// A reusable image carousel widget that can be used across the application.
///
/// This widget displays a carousel of images with optional loading state,
/// customizable options, and indicator dots.
class CommonImageCarousel<T> extends StatefulWidget {
  /// The list of data items to display in the carousel.
  final List<T>? items;

  /// The loading status of the carousel data.
  final DataStatus loadingStatus;

  /// Function to extract the image URL from each item.
  final String Function(T item) imageUrlExtractor;

  /// Optional function to handle tap on a carousel item.
  final void Function(T item)? onItemTap;

  /// Optional custom carousel options.
  final CarouselOptions? carouselOptions;

  /// Height of the carousel items.
  final double? itemHeight;

  /// Whether to show indicator dots below the carousel.
  final bool showIndicators;

  /// Color of the active indicator dot.
  final Color? activeIndicatorColor;

  /// Color of the inactive indicator dots.
  final Color? inactiveIndicatorColor;

  /// Size of the indicator dots.
  final double? indicatorSize;

  /// Spacing between indicator dots.
  final double? indicatorSpacing;

  /// Height of the shimmer placeholder during loading.
  final double? shimmerHeight;

  const CommonImageCarousel({
    super.key,
    required this.items,
    required this.imageUrlExtractor,
    this.loadingStatus = DataStatus.success,
    this.onItemTap,
    this.carouselOptions,
    this.itemHeight,
    this.showIndicators = true,
    this.activeIndicatorColor,
    this.inactiveIndicatorColor,
    this.indicatorSize,
    this.indicatorSpacing,
    this.shimmerHeight,
  });

  @override
  State<CommonImageCarousel<T>> createState() => _CommonImageCarouselState<T>();
}

class _CommonImageCarouselState<T> extends State<CommonImageCarousel<T>> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.loadingStatus == DataStatus.loading)
          CommonShimmer(
            width: MediaQuery.of(context).size.width,
            height: widget.shimmerHeight ?? 250.h,
            br: 10.r,
          )
        else if (widget.items?.isNotEmpty ?? false)
          Column(
            children: [
              CarouselSlider(
                options: _getCarouselOptions(),
                items: widget.items?.map((item) {
                  return Builder(
                    builder: (BuildContext context) {
                      return GestureDetector(
                        onTap: widget.onItemTap != null ? () => widget.onItemTap!(item) : null,
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          height: widget.itemHeight ?? widget.carouselOptions?.height ?? 300.h,
                          margin: EdgeInsets.symmetric(horizontal: 5.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            image: DecorationImage(
                              image: NetworkImage(widget.imageUrlExtractor(item)),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      );
                    },
                  );
                }).toList(),
              ),
              if (widget.showIndicators &&
                  widget.items?.length != null &&
                  widget.items!.length > 1) ...[
                10.verticalSpace,
                _buildIndicators(),
              ]
            ],
          ),
      ],
    );
  }

  /// Creates carousel options with the onPageChanged callback always set
  CarouselOptions _getCarouselOptions() {
    // Default options
    final defaultOptions = CarouselOptions(
      height: widget.itemHeight,
      viewportFraction: 1,
      enableInfiniteScroll: false,
      autoPlay: false,
      autoPlayInterval: const Duration(seconds: 3),
      autoPlayAnimationDuration: const Duration(milliseconds: 800),
      autoPlayCurve: Curves.fastOutSlowIn,
      enlargeCenterPage: true,
      onPageChanged: _onPageChanged,
    );

    // If custom options are provided, merge them with our onPageChanged callback
    if (widget.carouselOptions != null) {
      return CarouselOptions(
        height: widget.carouselOptions!.height,
        aspectRatio: widget.carouselOptions!.aspectRatio,
        viewportFraction: widget.carouselOptions!.viewportFraction,
        initialPage: widget.carouselOptions!.initialPage,
        enableInfiniteScroll: widget.carouselOptions!.enableInfiniteScroll,
        reverse: widget.carouselOptions!.reverse,
        autoPlay: widget.carouselOptions!.autoPlay,
        autoPlayInterval: widget.carouselOptions!.autoPlayInterval,
        autoPlayAnimationDuration: widget.carouselOptions!.autoPlayAnimationDuration,
        autoPlayCurve: widget.carouselOptions!.autoPlayCurve,
        enlargeCenterPage: widget.carouselOptions!.enlargeCenterPage,
        enlargeFactor: widget.carouselOptions!.enlargeFactor,
        scrollDirection: widget.carouselOptions!.scrollDirection,
        onPageChanged: _onPageChanged, // Always use our callback
        scrollPhysics: widget.carouselOptions!.scrollPhysics,
        pageSnapping: widget.carouselOptions!.pageSnapping,
        padEnds: widget.carouselOptions!.padEnds,
        clipBehavior: widget.carouselOptions!.clipBehavior,
        pauseAutoPlayOnTouch: widget.carouselOptions!.pauseAutoPlayOnTouch,
        pauseAutoPlayOnManualNavigate: widget.carouselOptions!.pauseAutoPlayOnManualNavigate,
        pauseAutoPlayInFiniteScroll: widget.carouselOptions!.pauseAutoPlayInFiniteScroll,
        pageViewKey: widget.carouselOptions!.pageViewKey,
        disableCenter: widget.carouselOptions!.disableCenter,
      );
    }

    return defaultOptions;
  }

  /// Handles page change events from the carousel
  void _onPageChanged(int index, CarouselPageChangedReason reason) {
    setState(() {
      _currentIndex = index;
    });
  }

  /// Builds the indicator dots for the carousel
  Widget _buildIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.items?.length ?? 0,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: _currentIndex == index
              ? (widget.indicatorSize ?? 8.w) * 1.5
              : widget.indicatorSize ?? 8.w,
          height: widget.indicatorSize ?? 8.h,
          margin: EdgeInsets.symmetric(horizontal: widget.indicatorSpacing ?? 4.w),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentIndex == index
                ? widget.activeIndicatorColor ?? Theme.of(context).primaryColor
                : widget.inactiveIndicatorColor ?? Colors.grey.shade300,
          ),
        ),
      ),
    );
  }
}
