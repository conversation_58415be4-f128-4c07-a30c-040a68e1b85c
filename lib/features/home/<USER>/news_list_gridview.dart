import 'package:flutter/material.dart';
import 'package:flutter_avif/flutter_avif.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app/core/routes/routes.dart';
import 'package:sf_app/core/theme/my_color_scheme.dart';
import 'package:sf_app/core/utils/convert_helper.dart';
import 'package:sf_app/core/widgets/common_shimmer.dart';

import 'package:sf_app/core/constants/string_constants.dart';
import 'package:sf_app/core/theme/color_pallette.dart';
import 'package:sf_app/core/theme/font_pallette.dart';
import 'package:sf_app/features/home/<USER>/home/<USER>';
import 'package:shimmer_animation/shimmer_animation.dart';

import '../../../core/constants/enums.dart';
import 'package:easy_localization/easy_localization.dart';

class NewsListGridView extends StatelessWidget {
  const NewsListGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Text(
            StringConstants.newsUpdates.tr(),
            style: FontPalette.bold16
                .copyWith(color: myColorScheme(context).titleColor),
          ),
        ),
        10.verticalSpace,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: BlocBuilder<HomeCubit, HomeState>(
            builder: (context, state) {
              return GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.newsDataList?.data.newsDataList.length ?? 0,
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.85,
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                ),
                itemBuilder: (context, index) {
                  final news = state.newsDataList?.data.newsDataList[index];
                  final isAvif = news?.coverUrl?.contains(".avif") ?? false;
                  if (state.newsUpdatesFetchStatus == DataStatus.loading &&
                      state.newsDataList?.data.newsDataList.isEmpty == true) {
                    return const NewsListItemShimmer();
                  }
                  return Bounceable(
                    onTap: () => Navigator.pushNamed(
                      context,
                      routeNewsDetailScreen,
                      arguments: {"id": news?.id ?? 0},
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 3,
                          child: _NewsImage(
                            imageUrl: news?.coverUrl ?? '',
                            isAvif: isAvif,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Padding(
                            padding: EdgeInsets.all(12.w),
                            child: _NewsContent(
                              title: news?.title ?? '',
                              datetime: ConvertHelper.formatDateMonthDay(
                                news?.createTime.toString() ?? '',
                              ),
                              timeAgo: _getTimeAgo(news?.createTime),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  String _getTimeAgo(dynamic createTime) {
    if (createTime == null) return '';
    
    try {
      final DateTime createDate = DateTime.parse(createTime.toString());
      final DateTime now = DateTime.now();
      final Duration difference = now.difference(createDate);
      
      if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return '';
    }
  }
}

class _NewsImageShimmer extends StatelessWidget {
  const _NewsImageShimmer();

  @override
  Widget build(BuildContext context) {
    return const CommonShimmer(
      width: double.infinity,
      height: double.infinity,
    );
  }
}

class _ErrorImage extends StatelessWidget {
  const _ErrorImage();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorPalette.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_not_supported_rounded,
            color: ColorPalette.primaryColor.withValues(alpha: 0.3),
            size: 24.w,
          ),
          SizedBox(height: 4.h),
          Text(
            'Image not available',
            style: FontPalette.semiBold9.copyWith(
              color: ColorPalette.primaryColor.withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }
}

class _NewsImage extends StatelessWidget {
  final double width;
  final double height;
  final String imageUrl;
  final bool isAvif;

  const _NewsImage({
    this.width = 120,
    this.height = 80,
    required this.imageUrl,
    required this.isAvif,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.r),
      child: SizedBox(
        width: width.w,
        height: height.h,
        child: isAvif
            ? AvifImage.network(
                imageUrl,
                fit: BoxFit.cover,
                loadingBuilder: (_, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const _NewsImageShimmer();
                },
                errorBuilder: (_, __, ___) => const _ErrorImage(),
              )
            : Image.network(
                imageUrl,
                fit: BoxFit.cover,
                loadingBuilder: (_, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const _NewsImageShimmer();
                },
                errorBuilder: (_, __, ___) => const _ErrorImage(),
              ),
      ),
    );
  }
}

class _NewsContent extends StatelessWidget {
  final String title;
  final String datetime;
  final String timeAgo;

  const _NewsContent({
    required this.title,
    required this.datetime,
    required this.timeAgo,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            title,
            style: FontPalette.semiBold11.copyWith(
              color: myColorScheme(context).titleColor,
              height: 1.2,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        8.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              datetime,
              style: FontPalette.semiBold11.copyWith(
                color: myColorScheme(context).labelColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              timeAgo,
              style: FontPalette.semiBold11.copyWith(
                color: myColorScheme(context).labelColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class NewsListItemShimmer extends StatelessWidget {
  const NewsListItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer(
      child: Container(
        decoration: BoxDecoration(
          color: myColorScheme(context).cardColor,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Shimmer for Image
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: myColorScheme(context).cardColor,
                  borderRadius: BorderRadius.circular(16.r),
                ),
              ),
            ),
            // Shimmer for Content
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(12.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Shimmer for Title lines
                    Container(
                      width: double.infinity,
                      height: 14.h,
                      color: myColorScheme(context).cardColor,
                      margin: EdgeInsets.only(bottom: 6.h),
                    ),
                    Container(
                      width: 0.8.sw,
                      height: 14.h,
                      color: myColorScheme(context).cardColor,
                      margin: EdgeInsets.only(bottom: 6.h),
                    ),
                    // Shimmer for date and time
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: 60.w,
                          height: 12.h,
                          color: myColorScheme(context).cardColor,
                        ),
                        Container(
                          width: 50.w,
                          height: 12.h,
                          color: myColorScheme(context).cardColor,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NewsListShimmer extends StatelessWidget {
  final int itemCount;

  const NewsListShimmer({
    super.key,
    this.itemCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: itemCount,
      itemBuilder: (context, index) => const NewsListItemShimmer(),
    );
  }
}
