{"FreezeAmount": "Сумма заморозки", "OR": "ИЛИ", "aDayAgo": "день назад", "aMinuteAgo": "минуту назад", "aMonthAgo": "месяц назад", "aadhaar": "удостоверение личности", "aadhaarCardUnder": "ID/Паспорт/ВУ находится на проверке", "aboutUs": "О нас", "accountInformation": "Информация об аккаунте", "accountInformationStatus1": "Удостоверение личности обновлено", "accountInformationStatus2": "Пароль кошелька успешно установлен", "accountInformationStatus3": "Номер телефона успешно зарегистрирован", "accountInformationStatus4": "Код Google успешно сгенерирован", "accountName": "Имя аккаунта", "accountNumber": "Номер счета", "accountTotal": "Всего на счете", "actualAmount": "Фактическая сумма:", "actualProfit": "Фактическая прибыль", "adAdminWill": "Администратор проверит вашу информацию, после чего вы сможете начать использовать платформу", "addAccountInfo": "Заполните информацию о вашем аккаунте", "addAddressWarning": "Поддерживается только 6 адресов для вывода средств, которые нельзя изменить.", "addWithdrawal": "Добавить адрес для вывода +", "addWithdrawalAddress": "Добавить адрес для вывода", "added": "Добавлено", "additionalAmount": "Дополнительная сумма", "additionalFunds": "Дополнительные средства", "additionalInvestment": "Дополнительные инвестиции", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addressNameTextController": "Название адреса", "addressTextHintText": "Пожалуйста, введите название адреса", "addressTextLabelText": "Название адреса", "alertCancel": "После подтверждения отмены вы продолжите получать выгоды.", "alertMsgAdminVerify": "Ваш аккаунт создан, пожалуйста, войдите снова.", "alertProceedToLogin": "Перейти к входу", "alertUnbind": "После подтверждения отвязки полная сумма по контракту будет возвращена в ваш кошелек прибыли через 30 рабочих дней, и вы продолжите получать доход до первых 15 рабочих дней.", "alreadyHaveAccount": "Уже есть аккаунт?  ", "amount": "Сумма", "amountExceedsMaximumAllowed": "Сумма превышает максимально допустимую", "amountMustBeInUnits": "Сумма должна быть в единицах", "amountMustBeMultipleOf": "Сумма должна быть кратной", "anHourAgo": "час назад", "and": "и ", "appName": "SF India : Smart Crypto", "appUpdateAvailable": "Доступно обновление приложения", "append": "Добавить", "appendRejectedAmount": "Добавить отклоненную сумму", "approvedSuccess": "<PERSON>аш аккаунт одобрен", "atLeast8character": "Минимум 8 символов, должен содержать цифры, как минимум одну заглавную и строчную букву и не должен содержать пробелов", "atLeast8characterWithoutUpperCase": "Минимум 8 символов, должен содержать цифры, одну строчную букву и не должен содержать пробелов", "auditing": "Проверка", "authentication": "Аутентификация", "automaticRenewal": "Автоматическое продление", "available": "Доступно", "availableBalance": "Доступный баланс", "availableContracts": "Доступные контракты", "availableFrom": "Доступно с", "availableUntil": "Доступно до", "backImageLarge": "Размер изображения обратной стороны слишком большой", "bank": "<PERSON><PERSON><PERSON><PERSON>", "bankAccount": "Банковский счет", "bankText": "Настройте банковский адрес для вывода средств", "benefitRules": "Правила выгоды", "bindIdCard": "Привязать ID/Паспорт/ВУ", "bindMobile": "Привязать номер телефона", "bonusWallet": "Бонусный кошелек", "buildYourProfile": "Создайте свою прибыль", "buy": "Купить", "buyIt": "Купить", "buyPosition": "Купить позицию", "buyPrice": "Цена покупки", "buyQuantity": "Количество покупки", "buyingPrice": "Цена покупки", "buyingTimeNotice": "Обратите внимание, что после 14:00 (ET) каждый день покупка будет недоступна. Спасибо за понимание и поддержку!", "cancel": "Отмена", "cancelSuccess": "Успешно отменено", "canceled": "Отменено", "captchaCode": "<PERSON>од капчи", "celebrityMentor": "Знаменитый наставник", "certificationCompleted": "Верификация уже завершена", "change": "Изменить", "changeAuthCode": "Изменить код Google аутентификации", "changeGoogleAuthentication": "Изменить код Google аутентификации", "changeIdentity": "Изменить личность", "changeLoginPassword": "Изменить пароль для входа", "changeNumber": "Изменить номер телефона", "changePassword": "Изменить пароль", "changePaymentPassword": "Изменить платежный пароль", "changeWalletPassword": "Изменить пароль кошелька", "chat": "Чат", "chats": "Чаты", "checkBoxSignupError": "Пожалуйста, подтвердите, что вы прочитали и согласны с Условиями использования и Политикой конфиденциальности", "checkEmail": "Проверить email", "checkPhone": "Проверить телефон", "chooseWallet": "Выбрать кошелек", "close": "Закрыть", "code": "<PERSON>од", "collectionWallet": "Кошелек для сбора", "commission": "Комиссия", "community": "Сообщество", "communityWallet": "Кошелек сообщества", "completed": "Завершено", "confirm": "Подтвердить", "confirmDelete": "Подтвердить удаление", "confirmHintText": "Подтвердить пароль", "confirmInvestment": "Подтвердить инвестицию", "confirmPasswordLabel": "Подтвердить пароль", "confirmWalletPassword": "Подтвердить пароль кошелька", "confirmWalletPasswordLabel": "Подтвердить пароль кошелька", "connected": "Подключено", "connecting": "Подключение...", "contactSupport": "Связаться с поддержкой", "continueTxt": "Продолжить", "contractAnnouncement": "Объявление о контракте", "contractList": "Список контрактов", "contractPrice": "Цена контракта", "contractSize": "Размер контракта", "contractType": "Ти<PERSON> контракта", "contractType1": "консервативный", "contractType2": "устойчивый", "contractType3": "радикальный", "contractedAmount": "Сумма по контракту", "contracts": "Контракты", "copied": "Скопировано", "copiedClipboard": "Скопировано в буфер обмена", "copy": "Копировать", "copyCode": "Скопируйте следующий код", "countryDialCode": "+7", "createTime": "Время создания", "crypto": "Крипто", "cumulativeIncome": "Накопленный доход", "currentVersion": "Текущая версия", "customValidatorMsg": " должно быть больше 0 символов", "customerService": "Служба поддержки", "customerSupport": "Поддержка клиентов", "cycle": "<PERSON>и<PERSON><PERSON>", "daily": "Ежедневно", "darkMode": "Темный режим", "datePrivacy": "Дата вступления в силу: 27/06/2023", "dateTerms": "Дата вступления в силу: 27/06/2023", "day": "день", "days": "<PERSON><PERSON><PERSON><PERSON>", "daysAgo": "дней назад", "daysOfHolding": "Дней удержания", "daysSinceUnbind": "Дней с отвязки", "deleteAddress": "Удалить адрес", "deleteAddressConfirmation": "Вы уверены, что хотите удалить этот адрес?", "deposit": "Депозит", "depositTip": "Что такое хеш транзакции (TxHash)?", "depositWallet": "Депозитный кошелек", "details": "Подробности", "didntRecieve": "Не получили код?  ", "directMembers": "Прямые участники", "done": "Готово", "dontHaveAccount": "Нет аккаунта?  ", "down": "<PERSON><PERSON><PERSON><PERSON>", "download": "Скачать", "downloadComplete": "Загрузка завершена", "downloadFailed": "Ошибка загрузки", "downloadUrlNotAvailable": "URL загрузки недоступен", "downloading": "Загрузка...", "downloadingUpdate": "Загрузка обновления...", "drawdown": "Снижение", "driverLicense": "Водительское удостоверение", "earningsLastDay": "Заработок за последний день", "editBank": "Редактировать банковский счет", "email": "Email", "emailAlready": "Email уже зарегистрирован", "emailError": "Вы не можете перевести на этот email адрес", "emailHintText": "Введите Email", "emailLabelText": "Email", "emailSupport": "Поддержка по email", "emailSupportDesc": "Отправьте нам email", "emailValidatorMsg": "Email недействителен", "emailVerification": "Код подтверждения Email", "empty": "Пока нет данных", "emptyAddressEmailMsg": "Введите адреса для вывода", "emptyAddressNameMsg": "Введите название адреса", "emptyEmailMsg": "Введите email", "emptyPasswordMsg": "Введите пароль", "emptyStringMsg": "Поле не может быть пустым", "emptyUserNameMsg": "Введите имя пользователя", "emptyWalletMsg": "Введите подтверждение пароля кошелька", "emptyWalletPasswordMsg": "Введите пароль кошелька", "endTime": "Время окончания", "enter4to12characters": "Введите от 4 до 12 символов, пробелы не допускаются", "enterAmount": "Введите сумму", "enterCaptcha": "Введите код капчи", "enterCode": "Введите код", "enterEmailCode": "Введите код подтверждения email", "enterExactly6Digits": "Введите ровно 6 цифр", "enterGoogleCode": "Введите код Google аутентификации", "enterPassword": "Введите пароль", "enterPhone": "Введите номер телефона", "enterPhoneNumber": "Введите номер телефона", "enterTheCode": "Введите код", "enterThePassword": "Введите пароли", "enterTransactionHash": "Введите хеш транзакции", "enterValidErc": "Введите действительный ERC20 адрес", "enterValidGoogleCode": "Введите действительный код Google", "enterValidPassword": "Введите действительный пароль", "enterValidPhone": "Введите действительный номер телефона", "enterValidTransactionHash": "Пожалуйста, введите действительный хеш транзакции", "enterValidTrc": "Введите действительный TRC20 адрес", "enterValidUsername": "Введите действительное имя пользователя", "enterVerification": "Введите код подтверждения", "enterWalletPassword": "Введите пароль кошелька", "enterWithdrawalAmount": "Введите сумму вывода", "enterYourFriendsEmail": "Введите email друга", "enter_email": "Введите email", "enter_phone_number": "Введите номер телефона", "erc20": "ERC 20", "erc20_description": "• Всегда 66 символов (hex формат)\n• Начинается с 0x (например, 0x59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "erc20_title": "✅ ERC20 (ETH/BSC/Polygon и т.д.):", "error": "Ошибка", "errorAuth": "Ошибка аутентификации, пожалуйста, войдите снова", "errorMsg": "Упс! Что-то пошло не так. Пожалуйста, попробуйте позже.", "expectedProfit": "Ожидаемая прибыль", "expert": "Эксперт", "failedToDownloadUpdate": "Не удалось загрузить обновление", "failedToInstallUpdate": "Не удалось установить обновление", "failedToLoadCommissionStats": "Не удалось загрузить статистику комиссий", "fifteenMin": "15 минут", "finance": "Финан<PERSON>ы", "finished": "Завершено", "firstGeneration": "Первое поколение", "fiveMin": "5 минут", "flat": "Фиксированный", "follow": "Подписаться", "followPurchaseDetails": "Детали последующей покупки", "followPurchaseDetailsTitle": "Детали последующей покупки", "followPurchaseRecords": "Записи последующих покупок", "followUpPeriod": "Время покупки", "followUpPurchaseAmount": "Сумма последующей покупки", "following": "Подписки", "forceUpdateRequired": "Требуется обновление", "forgotPassword": "Забыли пароль?", "frontImageLarge": "Размер изображения лицевой стороны слишком большой", "fundingThreshold": "Порог финансирования", "fundingWallet": "Кошелек финансирования", "gainers": "Растущие", "google": "google", "googleAuthCode": "<PERSON>од <PERSON> аутентификации", "googleAuthCodeHintText": "Пожалуйста, введите код google", "googleAuthCodeLabelText": " <PERSON>од <PERSON> аутентификации", "googleAuthSubTitle2Text": "Android, IOS - Google Authenticator", "googleAuthSubTitleText": "Скачайте приложение Google Authenticator", "googleAuthTitleText": "Настройка двухфакторной аутентификации", "googleAuthentication": "Google аутентификация", "googleCodeShouldBe": "Код Google должен быть 6 символов", "googleCodeUpdatedToast": "Код <PERSON> обновлен", "handlingFee": "Комиссия за обработку", "handlingFeeMessage": "Комиссия за обработку взимается каждый раз", "haveReadAndAgreedToThe": "Прочитал и согласен с", "helpCenter": "Центр помощи", "high": "Высокий", "hintPhone": "xxxxxxxxxx", "hintWallet": "0xxxxx00xx0x00xx0x0x0x0x0x0x0x0x0x", "home": "Главная", "hotStocks": "Горячие акции", "hoursAgo": "часов назад", "iAgree": "Регистрируясь, я соглашаюсь с ", "iHaveReadAndAgreed": "Я прочитал и согласен", "idCard": "Удостоверение личности", "idCardBack": "Обратная сторона ID/Паспорта/ВУ", "idCardConfirmation": "Подтверждение ID/Паспорта/ВУ", "idCardFront": "Лицевая сторона ID/Паспорта/ВУ", "idCardNumber": "Номер ID/Паспорта/ВУ", "idCardNumberErrorMessage": "Введите действительный номер ID/Паспорта/ВУ", "idCardNumberHint": "xxxxxxxxxxxx", "in": "в", "inProcess": "В процессе", "incorrectCaptcha": "Неверная капча", "incorrectOtp": "Неверный OTP", "information": "Информация", "initialCapital": "Начальный капитал", "installPermissionRequired": "Требуется разрешение на установку", "installationComplete": "Установка завершена", "installationFailed": "Установка не удалась", "installing": "Установка...", "installingUpdate": "Установка обновления...", "insufficientAvailableBalance": "Недостаточный доступный баланс", "insufficientBalance": "Недостаточный баланс", "intelligentFollowUpInvestment": "Интеллектуальные последующие инвестиции", "interestAmount": "Сумма процентов", "interestRate": "Процентная ставка", "invalidAmount": "Недействительная сумма", "invalidPassword": "Неверный пароль, попробуйте снова", "invalidPassword2": "Неверный платежный пароль", "investmentAmount": "Сумма инвестиций", "investmentUnits": "Инвестиционные единицы", "invitationCode": "Код приглашения", "invitationCodeError": "Код приглашения недействителен", "invitationCodeSuffix": "?invitationCode=", "invitationDescription": "Инвестируйте в будущее сегодня! Скачайте SF India : Smart Crypto сейчас и зарегистрируйтесь, чтобы начать.", "invitationLinkHintText": "http://supfut.com?invitationcode=abcdefg", "invitationLinkLabelText": "Ссылка приглашения", "jobPosition": "Должность", "justNow": "только что", "language": "Выбрать язык", "latestPrice": "Последняя цена", "latestTransaction": "Последняя\nцена\nтранзакции", "leadingConcept": "Ведущая концепция", "leadingIndustry": "Ведущая отрасль", "letsStart": "Начнем", "liveChat": "Живой чат", "liveChatDesc": "Чат с нашей командой поддержки", "lockPeriod": "Период блокировки", "lockedBalance": "Заблокированный баланс", "logIn": "Войти", "login": "Вход", "loginHintText": "Введите имя пользователя", "loginLabelText": "Имя пользователя", "loginLinkHintText": "http://supfut.com/login", "loginLinkLabelText": "Ссылка для входа", "loginLinkSuffix": "/login", "loginPasswordUpdatedToast": "Пароль для входа обновлен", "logout": "Выйти", "logoutConfirmation": "Вы уверены, что хотите выйти?", "losers": "Падающие", "lossesTimes": "Количество убытков", "low": "Низкий", "mainstreamCurrency": "Основная\nвалюта", "maintenanceNotice": "Уведомление о техническом обслуживании", "market": "Рынок", "marketOverview": "Обзор рынка", "marketUpdates": "Обновления рынка", "max": "<PERSON>а<PERSON><PERSON>", "maxDrawdown": "Максимальное снижение", "maximumAmount": "Максимальная сумма", "maximumPurchaseAmount": "Максимальная сумма покупки", "maximumWithdrawalAmount": "Максимальная сумма вывода", "memberInfo": "Информация о участнике", "members": "Участники", "mentorCommission": "Комиссия наставника", "mentors": "Наставники", "message": "Сообщение", "minimumAmount": "Минимальная сумма", "minimumAmountNotMeet": "Не достигнута минимальная сумма", "minimumPurchaseAmount": "Минимальная сумма покупки", "minimumWithdrawalAmount": "Минимальная сумма вывода", "minutesAgo": "минут назад", "missions": "Миссии", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthly": "Ежемесячно", "monthlyReturn": "Ежемесячная доходность", "mustBeMultipleOf": "должно быть кратно", "myContracts": "Мои контракты", "name": "Имя", "nameValidatorMsg": "Должно быть больше 0 символов", "networkError": "Ошибка сети", "networkLineLabelText": "Сетевая линия", "newPassword": "Новый пароль кошелька", "newPhoneNumberLabel": "Новый номер телефона", "newWalletPasswordLabel": "Новый пароль кошелька", "news": "Новости", "newsUpdates": "Обновления новостей", "next": "Далее", "noBalanceAvailable": "Нет доступного баланса", "noBalanceInformation": "Нет информации о балансе", "noDataAvailable": "Нет доступных данных", "noDetailsAvailable": "Нет доступных деталей", "noGalleryPermission": "Пожалуйста, включите разрешения на фото в настройках, чтобы продолжить", "noInternet": "Пожалуйста, проверьте ваше сетевое подключение", "noPurchasesYet": "Пока нет покупок", "noResultsFound": "Результаты не найдены", "notAvailable": "Недоступно", "notification": "Уведомления", "numOfContracts": "Количество контрактов", "numberOfTransactions": "Количество транзакций", "numberUpdatedToast": "Номер телефона обновлен", "numbering": "Нумерация", "officeCompany": "Офисная компания", "ok": "OK", "onBoardScreenSubtitle": "Начните инвестировать сегодня, и ваше будущее изменится к лучшему", "oneClickPurchase": "Покупка в один клик", "oneClickSmartInvestmentDescription": "Умные инвестиции в один клик высоко унифицированы и просты в торговле. Аналитики первыми выходят на рынок по институциональному каналу. Во время умных инвестиций в один клик не требуется никаких действий. Просто следуйте операции, освобождая руки, чтобы не забыть следовать покупке. Ежедневная прибыль будет автоматически восстанавливаться.", "oneClickSmartInvestmentInstructions": "Инструкции по умным инвестициям в один клик", "oneMin": "1 минута", "open": "Открыть", "openWallet": "Открыть кошелек", "optional": "Необязательно", "or": "ИЛИ", "orderDate": "Дата заказа", "orderNo": "Номер заказа", "otherMembers": "Другие участники", "otp": "Код подтверждения E-mail", "otpCodeError": "Введите ровно 6 цифр", "otp_phone": "Код подтверждения SMS", "passport": "Паспорт", "password": "Пароль", "passwordHint": "**********", "passwordHintText": "Введите пароль", "passwordNotEqual": "Введите совпадающий пароль", "passwordUpdatedSuccessfully": "Пароль успешно обновлен", "passwordUpdatedToast": "Пароль обновлен", "passwordValidationMsg": "Ваш пароль должен быть не менее 8 символов, содержать как минимум одну цифру и иметь смесь заглавных и строчных букв.", "pastEarnings": "Прошлые заработки", "paymentWallet": "Платежный кошелек", "pending": "В ожидании", "pending2": "В ожидании", "phone": "телефон", "phoneSupport": "Телефонная поддержка", "phoneSupportDesc": "Позвоните нам", "phoneVerification": "Подтверждение телефона", "phone_number": "Номер телефона", "platformCommission": "Комиссия платформы", "pleaseAcceptTheServiceAgreement": "Пожалуйста, примите соглашение об обслуживании", "pleaseComplete": "Пожал<PERSON>йста, завершите пополнение \n в течение 30 минут", "pleaseDepositSomeAmountToContinue": "Пожалуйста, внесите некоторую сумму, чтобы продолжить", "pleaseEnterAValidAmount": "Пожалуйста, введите действительную сумму", "pleaseEnterAValidInvestmentAmount": "Пожалуйста, введите действительную сумму инвестиций", "pleaseSelectAnInvestmentProduct": "Пожалуйста, выберите инвестиционный продукт", "pleaseWait": "Пожалуйста, подождите!", "portfolio": "Портфолио", "preview": "Предпросмотр", "privacyPolicy": "Политика конфиденциальности", "proceedToLogin": "Перейти к входу", "processing": "Обработка", "productName": "Название продукта", "products": "Продукты", "profile": "Профиль", "profit": "Прибыль", "profitAmount": "Сумма прибыли", "profitRatio": "Коэффициент прибыли", "profitTimes": "Количество прибылей", "profitWallet": "Кошелек прибыли", "progress": "Прогресс", "purchase": "Покупка", "purchaseAmountBetween": "Сумма покупки должна быть между {} и {}", "purchaseContracts": "Покупка контрактов", "purchaseDate": "Дата покупки", "purchaseList": "Список покупок", "purchasePrice": "Цена покупки", "purchaseTime": "Время покупки", "purchased": "Куплено", "purchasedContracts": "Купленные контракты", "purchasedProducts": "Купленные продукты", "qrCodeScan": "Пожалуйста, отсканируйте QR-код", "readLess": "Меньше", "readMore": "Больше", "readyToUpdate": "Готово к обновлению", "realtime": "В реальном времени", "reason": "Причина", "rechargeAddress": "Адрес пополнения", "rechargeOrderSubmittedSuccessfully": "Заказ на пополнение успешно отправлен", "rechargeQr": "QR-код пополнения", "records": "Записи", "referAndEarn": "Пригласи и заработай", "registerSuccess": "Ваш аккаунт создан, пожалуйста, войдите снова.", "rejected": "Отклонено", "report": "Сообщить о проблеме", "requestSuccess": "Запрос выполнен успешно", "resendCode": "Отправить код повторно", "reservedPhone": "Зарезервированный телефон", "resetPassword": "Сбросить пароль", "retracementRate": "Коэффициент отката", "retry": "Повторить", "retryUpdate": "Повторить", "returnRate": "Норма доходности", "revenue": "Доход", "revenueDetails": "Детали дохода", "reviewFailed": "Проверка не удалась, пожалуйста, отправьте снова", "search": "Поиск акций США...", "secondGeneration": "Второе поколение", "seconds": "секунд", "secondsUpper": "Секунд", "security": "Безопасность", "securityOptionsLabel": "Параметры безопасности", "securitySettings": "Настройки безопасности", "seeAll": "Смотреть все", "seeMore": "Подробнее", "selectWithdrawalAddress": "Выберите адрес вывода", "sell": "Продать", "sellDate": "Дата продажи", "sellPrice": "Цена продажи", "sellQuantity": "Количество продажи", "sellTime": "Время продажи", "sellingPrice": "Цена продажи", "sellingTime": "Время продажи", "sendCode": "Отправить код", "sendCodeAlert": "Код был отправлен на ваш аккаунт", "sendCodeToEmail": "Отправить код", "send_code": "Отправить код", "serviceAgreement": "Соглашение об обслуживании", "setAmount": "Установить сумму", "setWalletPassword": "Установить пароль кошелька", "settled": "Рассчитано", "share": "Поделиться", "shareText": "Зарегистрируйте реферала по ссылке-приглашению ниже", "signIn": "Войти", "signUp": "Зарегистрироваться", "signinAgree": "Регистрируясь, я соглашаюсь с ", "signingYouOut": "Выход из системы...", "singleAmount": "Единичная сумма", "size": "Размер", "skip": "Пропустить", "skipUpdate": "Пропустить", "smallRechargesBelow": "Малые пополнения ниже $100 не будут зачислены", "smartInvestment": "Умные инвестиции", "smartInvestmentCycle": "Цикл умных инвестиций", "smartInvestmentProducts": "Продукты умных инвестиций", "soldOut": "Распродано", "somethingWentWrong": "Что-то пошло не так", "somethingWentWrongTryAgain": "Что-то пошло не так, пожалуйста, попробуйте снова", "starMentor": "Звездный наставник", "startTime": "Время начала", "statistics": "Статистика", "stockCode": "Код акции", "stockName": "Название акции", "stockTrading": "Торговля акциями", "stocks": "Акции", "storagePermissionRequired": "Требуется разрешение на хранение", "subject": "Тема", "submit": "Отправить", "submitRecharge": "Отправить пополнение", "submitRequest": "Отправить запрос", "successful": "Успешно", "successfully": "Успешно", "suffixAddText": "Добавить +", "summary": "Сводка", "summaryTransfer": "Вы уверены, что хотите перевести следующую сумму?", "summaryWithdraw": "Вы уверены, что хотите вывести следующую сумму?", "support": "Поддержка", "systemNotifications": "Системные уведомления", "tasks": "Зада<PERSON>и", "tc": "Условия ", "termsAndConditions": "Условия и положения", "theStatisticalSample": "Статистическая выборка основана на данных за последние 30 дней при $10000.", "thirdGeneration": "Третье поколение", "thirtyMin": "30 минут", "thisRechargeAddressOneTime": "Этот адрес пополнения является одноразовым", "timesUpper": "Раз", "toCollectionWallet": " в Коллекционный кошелек", "toCommunity": "В сообщество", "toCommunityWallet": " в Кошелек сообщества", "toDepositWallet": " в Депозитный кошелек", "toProfitWallet": " в Кошелек прибыли", "todayProfit": "Прибыль за сегодня", "todaysChange": "Изменение\nза сегодня", "todaysStockMarket": "Фондовый рынок сегодня", "toolTipWarning2": "Контракт достиг максимального операционного масштаба и временно не может быть приобретен", "tootTipWarning": "Инвестиции всегда\nсвязаны с рисками,\nпоэтому важно провести\nсобственное исследование и\nпонимать потенциальные\nнедостатки.", "totalAmount": "Общая сумма", "totalMembers": "Всего участников", "totalRevenue": "Общий доход", "tradingDays": "Торговые дни", "tradingWallet": "Торговый кошелек", "transactionCycle": "Цикл транзакций", "transactionHash": "Хэш транзакции", "transactionHashDescription": "🛡️ Используется для отслеживания и проверки статуса транзакции в блокчейне", "transactionHashDescription2": "Считайте это цифровой квитанцией вашей транзакции.", "transactionRecords": "Записи транзакций", "transactionsHistory": "История транзакций", "transfer": "Перевод", "transferChargedAmount": "С оставшихся {} будет взиматься комиссия 20% (всего {})", "transferExpectedReceive": "Ожидаемая сумма к получению {}", "transferFreeAmount": "Вы можете перевести {} бесплатно", "transferTo": "Перевести ", "transferred": "Переведено", "trc20": "TRC 20", "trc20_description": "• Всегда 64 символа (hex формат)\n• Без 0x в начале (например, 59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "trc20_title": "✅ TRC20 (сеть TRON):", "tryAgain": "Попробовать снова", "tutorCommission": "Комиссия наставника", "type": "Тип", "typeBack": "обратная сторона", "typeCollection": "сбор", "typeConfirmWallet": "подтверждение кошелька", "typeDeposit": "депозит", "typeFront": "лицевая сторона", "typeGoogle": "google", "typePaymentERC": "ERC20", "typePaymentTRC": "TRC20", "unBindSuccess": "Успешно отвязано", "unbind": "Отвязать", "unknown": "Неизвестно", "unknownVersion": "Неизвестно", "up": "Ввер<PERSON>", "upcoming": "Предстоящие", "updateNow": "Обновить сейчас", "updateRequired": "Требуется обновление", "updating": "Обновление...", "uploadBackIdCard": "Загрузить обратную сторону ID/паспорта/ВУ", "uploadFrontIdCard": "Загрузить лицевую сторону ID/паспорта/ВУ", "uploadIdBack": "Загрузить обратную сторону ID/паспорта/ВУ", "uploadIdCard": "Загрузить ID/паспорт/ВУ", "uploadIdFront": "Загрузить лицевую сторону ID/паспорта/ВУ", "uploadImageError": "Размер файла слишком большой", "urlPrefix": "https://", "usMarket": "Рынок США", "usdt": "USDT", "user": "Пользователь", "validAddressNameMsg": "Введите действительное название адреса", "verify": "Проверить", "viewAll": "Смотреть все", "vipLevelError": "Вам нужно достичь уровня {} для покупки", "vipNotice": "Недостаточный уровень VIP", "vipNoticeDescription": "Требуется минимальный уровень VIP {} для подписки на этого наставника", "volume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wallet": "кошелек", "walletAddressExists": "Адрес вывода уже существует", "walletNameExists": "Название адреса уже существует", "walletPass": "Пароль кошелька", "walletPassword": "Пароль кошелька", "walletPasswordShouldBe": "Пароль кошелька должен быть 6 символов", "walletUpdatedToast": "Пароль кошелька обновлен", "warning": "Предупреждение", "weHaveSent": "Мы отправили одноразовый пароль на ваш зарегистрированный email", "week": "Неделя", "welcome": "Добро пожаловать!", "whatIsATransactionHash": "Что такое хэш транзакции (TxHash)?", "whatIsATransactionHashDescription": "Хэш транзакции - это уникальный ID, присваиваемый каждой транзакции в блокчейне. Он выглядит как длинная строка букв и цифр (например, 0x59d24d44... или 59d24d44...).", "whatsappSupport": "Поддержка WhatsApp", "whatsappSupportDesc": "Свяжитесь с нами через WhatsApp", "willBeSentToProfitWallet": "Будет отправлено в кошелек прибыли", "winRate": "Процент выигрышей", "withdraw": "Вывод", "withdrawAddressHintText": "Пожалуйста, введите адрес вывода", "withdrawAddressLabelText": "Адрес вывода", "withdrawAddresses": "Адреса вывода", "withdrawHistory": "История выводов", "withdrawInvestment": "Вывести инвестицию", "withdrawLimit": "Вы не можете вывести меньше $100", "withdrawal": "Вывод", "withdrawalAmount": "Сумма вывода", "withdrawalFee": "Комиссия за вывод :", "withdrawnProfit": "Выведенная прибыль", "workingAge": "Рабо<PERSON>ий стаж", "years": "лет", "yearsOfExperience": "Лет опыта", "zeroContractToast": "Нет доступных контрактов", "unavailableFunds": "Недоступные средства", "availableFunds": "Доступные средства", "welcomeTo": "Добро пожаловать в SIS", "hi": "Привет"}