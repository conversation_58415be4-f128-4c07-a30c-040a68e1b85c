{"FreezeAmount": "Užšaldyta suma", "OR": "ARBA", "aDayAgo": "<PERSON><PERSON><PERSON>", "aMinuteAgo": "p<PERSON><PERSON> min<PERSON>", "aMonthAgo": "prie<PERSON> mėnesį", "aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarCardUnder": "ID/Pasas/Vairuotojo p<PERSON>s tikrinamas", "aboutUs": "Apie mus", "accountInformation": "Paskyros informacija", "accountInformationStatus1": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> at<PERSON>", "accountInformationStatus2": "Pinigin<PERSON><PERSON> sėkmingai nustatytas", "accountInformationStatus3": "Telefono numeris sėkmingai užregistruotas", "accountInformationStatus4": "Google kodas sėkmingai sugeneruotas", "accountName": "<PERSON><PERSON><PERSON>", "accountNumber": "Paskyros numeris", "accountTotal": "<PERSON>ra paskyros suma", "actualAmount": "<PERSON>ak<PERSON><PERSON> suma:", "actualProfit": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "adAdminWill": "<PERSON><PERSON> patikrins j<PERSON> informaciją, po to galėsite pradėti naudotis platforma", "addAccountInfo": "Užpildykite savo paskyros informaciją", "addAddressWarning": "Palaikomi tik 6 išėmimo adresai ir jų negalima keisti.", "addWithdrawal": "Pridė<PERSON> išė<PERSON> +", "addWithdrawalAddress": "<PERSON><PERSON><PERSON><PERSON> išė<PERSON>", "added": "<PERSON><PERSON><PERSON><PERSON>", "additionalAmount": "Pa<PERSON><PERSON><PERSON> suma", "additionalFunds": "<PERSON><PERSON><PERSON><PERSON>", "additionalInvestment": "Papildoma investicija", "address": "<PERSON><PERSON><PERSON>", "addressNameTextController": "Adreso pavadin<PERSON>s", "addressTextHintText": "Prašome įvesti adreso pavadinimą", "addressTextLabelText": "Adreso pavadin<PERSON>s", "alertCancel": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>, toliau gausite naudą.", "alertMsgAdminVerify": "<PERSON><PERSON><PERSON><PERSON> paskyra sukurta, prašome prisijungti iš naujo.", "alertProceedToLogin": "Pereiti prie prisijungimo", "alertUnbind": "<PERSON><PERSON><PERSON><PERSON>, visa suma kontrakte bus grąžinta į jūsų pelno piniginę po 30 darbo dienų, ir jūs to<PERSON>u gausite pajamas iki pirmųjų 15 darbo dienų.", "alreadyHaveAccount": "Jau turite paskyrą?  ", "amount": "<PERSON><PERSON>", "amountExceedsMaximumAllowed": "Suma viršija maksimaliai le<PERSON>ž<PERSON>", "amountMustBeInUnits": "<PERSON>ma turi būti vienetais", "amountMustBeMultipleOf": "<PERSON>ma turi būti kart<PERSON>s", "anHourAgo": "<PERSON><PERSON><PERSON> v<PERSON>", "and": "ir ", "appName": "SF India : Smart Crypto", "appUpdateAvailable": "Galimas programos atnaujinimas", "append": "<PERSON><PERSON><PERSON><PERSON>", "appendRejectedAmount": "<PERSON><PERSON><PERSON>ti atmestą sumą", "approvedSuccess": "Jūsų paskyra patvirtinta", "atLeast8character": "Mažiausiai 8 simboliai, turi tur<PERSON><PERSON>, mažiausiai vieną didžiąją ir mažąją raidę ir neturi turėti tarpų", "atLeast8characterWithoutUpperCase": "Mažiausiai 8 simboliai, turi tur<PERSON><PERSON>, v<PERSON><PERSON>ę ir neturi turėti tarpų", "auditing": "Auditas", "authentication": "Autentifikacija", "automaticRenewal": "Automatinis <PERSON>", "available": "<PERSON><PERSON><PERSON>", "availableBalance": "<PERSON><PERSON><PERSON> balan<PERSON>", "availableContracts": "<PERSON><PERSON>mi <PERSON>", "availableFrom": "<PERSON><PERSON><PERSON> nuo", "availableUntil": "<PERSON><PERSON><PERSON> iki", "backImageLarge": "<PERSON><PERSON><PERSON><PERSON><PERSON> pu<PERSON> vaizdo dydis per did<PERSON>s", "bank": "Bankas", "bankAccount": "Banko sąskaita", "bankText": "Nustatykite banko adresą išėmimui", "benefitRules": "<PERSON><PERSON><PERSON>", "bindIdCard": "Susieti ID/Pasą/Vairuotojo p<PERSON>žymėjimą", "bindMobile": "Susieti mobiliojo telefono numerį", "bonusWallet": "<PERSON><PERSON><PERSON>", "buildYourProfile": "Sukurkite savo pelną", "buy": "<PERSON><PERSON><PERSON>", "buyIt": "<PERSON><PERSON><PERSON> tai", "buyPosition": "Pirkimo <PERSON>", "buyPrice": "<PERSON><PERSON><PERSON> kaina", "buyQuantity": "<PERSON><PERSON><PERSON> k<PERSON>", "buyingPrice": "<PERSON><PERSON><PERSON> kaina", "buyingTimeNotice": "Atkreipkite dėmesį, kad po 14:00 (ET) kasdien pirkimas nebegalės būti atlie<PERSON>. Dėkojame už supratimą ir palaikymą!", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "cancelSuccess": "Sėkmingai atšaukta", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "captchaCode": "<PERSON><PERSON> koda<PERSON>", "celebrityMentor": "<PERSON><PERSON><PERSON>", "certificationCompleted": "<PERSON><PERSON><PERSON><PERSON> jau baigtas", "change": "<PERSON><PERSON><PERSON>", "changeAuthCode": "Keisti Google autentifikacijos kodą", "changeGoogleAuthentication": "Keisti Google autentifikacijos kodą", "changeIdentity": "<PERSON><PERSON>i tapat<PERSON>bę", "changeLoginPassword": "Keisti prisijungimo slaptažodį", "changeNumber": "Keisti mobiliojo telefono numerį", "changePassword": "<PERSON><PERSON><PERSON> slaptažodį", "changePaymentPassword": "<PERSON><PERSON><PERSON> m<PERSON> slaptažodį", "changeWalletPassword": "<PERSON><PERSON><PERSON> slaptažodį", "chat": "Pokalbis", "chats": "Pokalbiai", "checkBoxSignupError": "<PERSON><PERSON><PERSON><PERSON> nuro<PERSON>, kad per<PERSON> ir sutinkate su Sąlygomis ir nuostatomis bei Privatumo politika", "checkEmail": "Patikrinti el. paštą", "checkPhone": "Patikrinti telefoną", "chooseWallet": "<PERSON><PERSON><PERSON><PERSON>", "close": "Uždaryti", "code": "<PERSON><PERSON>", "collectionWallet": "<PERSON><PERSON><PERSON>", "commission": "<PERSON><PERSON><PERSON><PERSON>", "community": "Bendruomenė", "communityWallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "completed": "Baigta", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "confirmDelete": "Patvir<PERSON><PERSON>", "confirmHintText": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "confirmInvestment": "<PERSON><PERSON><PERSON><PERSON> invest<PERSON>", "confirmPasswordLabel": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "confirmWalletPassword": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "confirmWalletPasswordLabel": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "connected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connecting": "<PERSON><PERSON><PERSON>...", "contactSupport": "Susisiekti su palaikymu", "continueTxt": "<PERSON><PERSON><PERSON><PERSON>", "contractAnnouncement": "<PERSON><PERSON><PERSON><PERSON>", "contractList": "Kontraktų sąrašas", "contractPrice": "<PERSON><PERSON><PERSON><PERSON> kaina", "contractSize": "<PERSON><PERSON><PERSON><PERSON>", "contractType": "Kontrak<PERSON> tip<PERSON>", "contractType1": "konservatyvus", "contractType2": "<PERSON><PERSON><PERSON>", "contractType3": "radikalus", "contractedAmount": "<PERSON><PERSON><PERSON><PERSON> suma", "contracts": "Kontraktai", "copied": "Nukopijuota", "copiedClipboard": "Nukopijuota į iškarpinę", "copy": "Ko<PERSON><PERSON><PERSON><PERSON>", "copyCode": "Nukopijuokite šį kodą", "countryDialCode": "+370", "createTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "crypto": "Kriptovaliuta", "cumulativeIncome": "<PERSON><PERSON><PERSON><PERSON> paja<PERSON>", "currentVersion": "<PERSON><PERSON><PERSON><PERSON> versija", "customValidatorMsg": " turi būti daugiau nei 0 simbolių", "customerService": "Klientų aptarnavimo tarnyba", "customerSupport": "Klientų palaikym<PERSON>", "cycle": "<PERSON><PERSON><PERSON>", "daily": "Kasdien", "darkMode": "<PERSON><PERSON>", "datePrivacy": "Įsigaliojimo data: 2023-06-27", "dateTerms": "Įsigaliojimo data: 2023-06-27", "day": "diena", "days": "dienų", "daysAgo": "<PERSON><PERSON><PERSON>", "daysOfHolding": "<PERSON><PERSON><PERSON>", "daysSinceUnbind": "Dienų nuo atsiejimo", "deleteAddress": "<PERSON><PERSON><PERSON><PERSON>", "deleteAddressConfirmation": "Ar tikrai norite ištrinti šį adresą?", "deposit": "Indėlis", "depositTip": "Kas yra transak<PERSON> hash (TxHash)?", "depositWallet": "Indėlių piniginė", "details": "<PERSON><PERSON><PERSON><PERSON>", "didntRecieve": "Negavote kodo?  ", "directMembers": "Tiesioginiai nariai", "done": "Atlikta", "dontHaveAccount": "Neturite paskyros?  ", "down": "<PERSON><PERSON><PERSON>", "download": "Atsisiųsti", "downloadComplete": "At<PERSON>iu<PERSON><PERSON> baigtas", "downloadFailed": "Atsisiu<PERSON><PERSON>", "downloadUrlNotAvailable": "Atsisiuntimo URL nepasiekiamas", "downloading": "Atsisiunčiama...", "downloadingUpdate": "Atsisiunčiamas atnaujinimas...", "drawdown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "driverLicense": "Vairuotojo p<PERSON>ž<PERSON>jimas", "earningsLastDay": "Paskutin<PERSON><PERSON>", "editBank": "Redaguoti banko sąskaitą", "email": "El. <PERSON>", "emailAlready": "El. paštas jau užregistruotas", "emailError": "Negalite pervesti į šį el. pašto adresą", "emailHintText": "Įveskite el. paštą", "emailLabelText": "El. <PERSON>", "emailSupport": "<PERSON><PERSON> p<PERSON><PERSON><PERSON>", "emailSupportDesc": "Atsiųskite mums el. <PERSON>", "emailValidatorMsg": "El. paštas neteisingas", "emailVerification": "<PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "empty": "<PERSON><PERSON> kas nėra duomenų", "emptyAddressEmailMsg": "Įveskite i<PERSON><PERSON><PERSON><PERSON> adresus", "emptyAddressNameMsg": "Įveskite adreso pavadinimą", "emptyEmailMsg": "Įveskite el. paštą", "emptyPasswordMsg": "Įveskite slaptažodį", "emptyStringMsg": "<PERSON><PERSON> negali bū<PERSON>", "emptyUserNameMsg": "Įveskite varto<PERSON><PERSON> vardą", "emptyWalletMsg": "Įveskite pinigin<PERSON><PERSON>", "emptyWalletPasswordMsg": "Įveskite piniginės slaptažodį", "endTime": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "enter4to12characters": "Įveskite nuo 4 iki 12 simbolių, tarpai neleidžiami", "enterAmount": "Įveskite sumą", "enterCaptcha": "Įveskite captcha kodą", "enterCode": "Įveskite kodą", "enterEmailCode": "Įveskite el. pa<PERSON><PERSON> pat<PERSON><PERSON> k<PERSON>", "enterExactly6Digits": "Įveskite tiksliai 6 skaitmenis", "enterGoogleCode": "Įveskite Google Authentication kodą", "enterPassword": "Įveskite slaptažodį", "enterPhone": "Įveskite telefono numerį", "enterPhoneNumber": "Įveskite telefono numerį", "enterTheCode": "Įveskite kodą", "enterThePassword": "Įveskite <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterTransactionHash": "Įveskite transakcijos hash", "enterValidErc": "Įveskite galiojantį ERC20 adresą", "enterValidGoogleCode": "Įveskite galiojantį Google kodą", "enterValidPassword": "Įveskite galiojantį slaptažodį", "enterValidPhone": "Įveskite galiojantį mobiliojo telefono numerį", "enterValidTransactionHash": "Prašome įvesti galiojantį transakcijos hash", "enterValidTrc": "Įveskite galiojantį TRC20 adresą", "enterValidUsername": "Įveskite galiojantį var<PERSON><PERSON><PERSON> vardą", "enterVerification": "Įveskite pat<PERSON><PERSON><PERSON> kod<PERSON>", "enterWalletPassword": "Įveskite piniginės slaptažodį", "enterWithdrawalAmount": "Įveskite i<PERSON><PERSON><PERSON><PERSON> sumą", "enterYourFriendsEmail": "Įveskite savo draugo el. pa<PERSON>ą", "enter_email": "Įveskite el. paštą", "enter_phone_number": "Įveskite telefono numerį", "erc20": "ERC 20", "erc20_description": "• Always 66 characters (hex format)\n• Starts with 0x (e.g., 0x59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "erc20_title": "✅ ERC20 (ETH/BSC/Polygon, etc.):", "error": "<PERSON><PERSON><PERSON>", "errorAuth": "Autentifikacija <PERSON>vyko, prašome prisijungti iš naujo", "errorMsg": "Ups! Ka<PERSON><PERSON> nutiko. Prašome bandyti vėliau.", "expectedProfit": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "expert": "Ekspertas", "failedToDownloadUpdate": "Nepavyko atsisiųsti atnaujinimo", "failedToInstallUpdate": "Nepavyko įdiegti atnaujinimo", "failedToLoadCommissionStats": "Nepavyko įkelti komisinių statistikos", "fifteenMin": "15 minučių", "finance": "Fin<PERSON><PERSON>", "finished": "Baigta", "firstGeneration": "Pirma karta", "fiveMin": "5 minutės", "flat": "Plokš<PERSON><PERSON>", "follow": "<PERSON><PERSON>i", "followPurchaseDetails": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "followPurchaseDetailsTitle": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "followPurchaseRecords": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> įrašai", "followUpPeriod": "<PERSON><PERSON><PERSON>", "followUpPurchaseAmount": "<PERSON><PERSON><PERSON><PERSON> pirkimo suma", "following": "<PERSON><PERSON><PERSON>", "forceUpdateRequired": "Re<PERSON>ling<PERSON> at<PERSON>jin<PERSON>", "forgotPassword": "Pamiršote slaptažodį?", "frontImageLarge": "<PERSON><PERSON><PERSON><PERSON><PERSON> pu<PERSON>s vaizdo dydis per did<PERSON>s", "fundingThreshold": "<PERSON><PERSON><PERSON><PERSON>", "fundingWallet": "<PERSON><PERSON><PERSON><PERSON>", "gainers": "<PERSON><PERSON><PERSON>", "google": "google", "googleAuthCode": "Google Authentication kodas", "googleAuthCodeHintText": "Prašome įvesti google kodą", "googleAuthCodeLabelText": " Google Authentication kodas", "googleAuthSubTitle2Text": "Android, IOS - Google Authenticator", "googleAuthSubTitleText": "Atsisiųskite Google Authenticator programą", "googleAuthTitleText": "Nustatykite dviejų veiksnių autentifikaciją", "googleAuthentication": "Google Authentication", "googleCodeShouldBe": "Google kodas turi būti 6 simbolių", "googleCodeUpdatedToast": "Google kodas atnaujintas", "handlingFee": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "handlingFeeMessage": "Tvarky<PERSON> mokestis reikalingas kiekvieną kartą", "haveReadAndAgreedToThe": "Perskaičiau ir sutinku su", "helpCenter": "Pagalbos centras", "high": "Aukštas", "hintPhone": "xxxxxxxxxx", "hintWallet": "0xxxxx00xx0x00xx0x0x0x0x0x0x0x0x0x", "home": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hotStocks": "<PERSON><PERSON><PERSON><PERSON>", "hoursAgo": "<PERSON><PERSON><PERSON> v<PERSON>", "iAgree": "Registruodamasis, sutinku su ", "iHaveReadAndAgreed": "Perskaičiau ir sutinku", "idCard": "ID kortelė", "idCardBack": "ID/Pasas/Vairuotojo pažym<PERSON>jimas galinė pusė", "idCardConfirmation": "ID/Paso/Vairuotojo pažym<PERSON>jimo pat<PERSON>s", "idCardFront": "ID/Pasas/Vairuotojo pažym<PERSON>jimas priekinė pusė", "idCardNumber": "ID/Paso/Vairuotojo pažymėjimo numeris", "idCardNumberErrorMessage": "Įveskite galiojantį ID/Paso/Vairuotojo paž<PERSON>jimo numerį", "idCardNumberHint": "xxxxxxxxxxxx", "in": "per", "inProcess": "Vykdoma", "incorrectCaptcha": "Neteisingas Captcha", "incorrectOtp": "Neteisingas OTP", "information": "Informacija", "initialCapital": "<PERSON><PERSON><PERSON><PERSON>", "installPermissionRequired": "Reikalingas diegimo leidimas", "installationComplete": "<PERSON><PERSON><PERSON> baigtas", "installationFailed": "<PERSON><PERSON><PERSON>", "installing": "Diegiama...", "installingUpdate": "<PERSON><PERSON><PERSON> at<PERSON>...", "insufficientAvailableBalance": "Nepaka<PERSON><PERSON> galimas balansas", "insufficientBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON> balan<PERSON>s", "intelligentFollowUpInvestment": "Protinga papildoma investicija", "interestAmount": "Palūkanų suma", "interestRate": "Palūkan<PERSON> norma", "invalidAmount": "Neteisinga suma", "invalidPassword": "<PERSON><PERSON><PERSON><PERSON>, bandykite dar kartą", "invalidPassword2": "Neteisingas mok<PERSON>", "investmentAmount": "Investicijos suma", "investmentUnits": "Investicijos vienetai", "invitationCode": "Pakvietimo koda<PERSON>", "invitationCodeError": "Pakvietimo kodas netei<PERSON>", "invitationCodeSuffix": "?invitationCode=", "invitationDescription": "Investuokite į ateitį šiandien! Atsisiųskite SF India : <PERSON> Crypto dabar ir užsir<PERSON>, kad <PERSON>.", "invitationLinkHintText": "http://supfut.com?invitationcode=abcdefg", "invitationLinkLabelText": "Pakvietimo nuoroda", "jobPosition": "Darbo pozicija", "justNow": "ką tik", "language": "<PERSON><PERSON><PERSON><PERSON> kal<PERSON>", "latestPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaina", "latestTransaction": "Naujausia\nTransakcijos\nKai<PERSON>", "leadingConcept": "Pirmaujanti k<PERSON>", "leadingIndustry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "letsStart": "Pradėkime", "liveChat": "<PERSON>iesio<PERSON><PERSON> pokalbis", "liveChatDesc": "Kalbėkitės su mūsų palaikymo komanda", "lockPeriod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lockedBalance": "Užrakintas balansas", "logIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loginHintText": "Įveskite varto<PERSON><PERSON> vardą", "loginLabelText": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>", "loginLinkHintText": "http://supfut.com/login", "loginLinkLabelText": "Prisijungimo nuoroda", "loginLinkSuffix": "/login", "loginPasswordUpdatedToast": "Prisijun<PERSON><PERSON>", "logout": "<PERSON>si<PERSON><PERSON><PERSON>", "logoutConfirmation": "Ar tikrai norite atsijungti?", "losers": "<PERSON><PERSON><PERSON><PERSON>", "lossesTimes": "Nuostolių kartų", "low": "<PERSON><PERSON><PERSON>", "mainstreamCurrency": "Pagrindinė\nValiuta", "maintenanceNotice": "Priežiū<PERSON>", "market": "<PERSON><PERSON><PERSON>", "marketOverview": "<PERSON><PERSON><PERSON>", "marketUpdates": "<PERSON><PERSON><PERSON>", "max": "<PERSON><PERSON>", "maxDrawdown": "<PERSON><PERSON><PERSON><PERSON>", "maximumAmount": "<PERSON><PERSON><PERSON><PERSON> suma yra", "maximumPurchaseAmount": "<PERSON><PERSON><PERSON><PERSON> pirkimo suma", "maximumWithdrawalAmount": "<PERSON><PERSON><PERSON><PERSON> suma yra", "memberInfo": "Nario informacija", "members": "<PERSON><PERSON><PERSON>", "mentorCommission": "<PERSON><PERSON><PERSON> komisinis", "mentors": "Mentoriai", "message": "Žinutė", "minimumAmount": "Minimali suma yra", "minimumAmountNotMeet": "Minimali suma nepasiekta", "minimumPurchaseAmount": "Minimali pirkimo suma", "minimumWithdrawalAmount": "Minimali išėmimo suma yra", "minutesAgo": "prieš minutes", "missions": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON> mėnesį", "monthlyReturn": "Mėnesinis g<PERSON>", "mustBeMultipleOf": "turi b<PERSON>ti kart<PERSON>s", "myContracts": "<PERSON><PERSON>", "name": "Vardas", "nameValidatorMsg": "<PERSON><PERSON> būti daugiau nei 0 simbolių", "networkError": "<PERSON><PERSON><PERSON>", "networkLineLabelText": "Tinklo linija", "newPassword": "<PERSON><PERSON><PERSON>", "newPhoneNumberLabel": "Naujas telefono numeris", "newWalletPasswordLabel": "<PERSON><PERSON><PERSON>", "news": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newsUpdates": "Naujienų atnaujinimai", "next": "Toliau", "noBalanceAvailable": "<PERSON><PERSON><PERSON> galimo balanso", "noBalanceInformation": "<PERSON>ėra balanso informacijos", "noDataAvailable": "Nėra duomenų", "noDetailsAvailable": "Nėra detalių", "noGalleryPermission": "Prašome įjungti nuotraukų leidimus nustatymuose, kad tęst<PERSON>", "noInternet": "Prašome patikrinti tinklo ryšį", "noPurchasesYet": "<PERSON><PERSON> kas nėra pirkimų", "noResultsFound": "Rezultatų nerasta", "notAvailable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notification": "Pranešimai", "numOfContracts": "Kontrakt<PERSON> skaič<PERSON>", "numberOfTransactions": "Transakcij<PERSON> s<PERSON>", "numberUpdatedToast": "Telefono numeris atnaujintas", "numbering": "Numeravimas", "officeCompany": "Biuro įmonė", "ok": "G<PERSON><PERSON>", "onBoardScreenSubtitle": "Pradėkite investuoti šiandien ir jūsų ateitis pasikeis į gerąją pusę", "oneClickPurchase": "Pirkimas vienu paspaudimu", "oneClickSmartInvestmentDescription": "Protinga investicija vienu paspaudimu yra labai suvienodinta ir lengva prekybai. Analitikai pirmieji pateks į rinką per institucinį kanalą. Protingos investicijos vienu paspaudimu laikotarpiu nereikia jokių operacijų. Tiesiog sekite operaciją, i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rank<PERSON>, kad išven<PERSON>um<PERSON>te pamiršimo sekti pirkimą. Kasdieniai pelnas bus automatiškai atkurtas.", "oneClickSmartInvestmentInstructions": "Protingos investicijos vienu paspaudimu instrukcijos", "oneMin": "1 minutė", "open": "<PERSON><PERSON><PERSON><PERSON>", "openWallet": "<PERSON><PERSON><PERSON><PERSON>", "optional": "Neprivaloma", "or": "ARBA", "orderDate": "Užsakymo data", "orderNo": "Užsakymo Nr", "otherMembers": "<PERSON>i nariai", "otp": "<PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "otpCodeError": "Įveskite tiksliai 6 skaitmenis", "otp_phone": "SMS patvir<PERSON><PERSON> kodas", "passport": "Pasas", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passwordHint": "**********", "passwordHintText": "Įveskite slaptažodį", "passwordNotEqual": "Įveskite atitinkantį slaptažodį", "passwordUpdatedSuccessfully": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atnaujintas", "passwordUpdatedToast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passwordValidationMsg": "Jūsų slaptažodis turi būti mažiausiai 8 simbolių ilgio, turėti mažiausiai vieną skaičių ir turėti didžiųjų ir mažųjų raidžių mišinį.", "pastEarnings": "Ankstesni uždarbiai", "paymentWallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "pending2": "<PERSON><PERSON><PERSON>", "phone": "telefonas", "phoneSupport": "Telefono palaikymas", "phoneSupportDesc": "Paskambinkite mums", "phoneVerification": "Telefono patvirtinimas", "phone_number": "Telefono numeris", "platformCommission": "<PERSON><PERSON>", "pleaseAcceptTheServiceAgreement": "Prašome priimti paslaugų sutartį", "pleaseComplete": "Prašome užbaigti papildymą \n per 30 minučių", "pleaseDepositSomeAmountToContinue": "Prašome įnešti sumą, kad tęstum<PERSON>te", "pleaseEnterAValidAmount": "Prašome įvesti galiojančią sumą", "pleaseEnterAValidInvestmentAmount": "Prašome įvesti galiojančią investicijos sumą", "pleaseSelectAnInvestmentProduct": "Prašome pasirinkti investicijos produktą", "pleaseWait": "<PERSON>rašome palaukti!", "portfolio": "Port<PERSON>lis", "preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "privacyPolicy": "Privatumo politika", "proceedToLogin": "Pereiti prie prisijungimo", "processing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productName": "Produkto pavadinimas", "products": "Produktai", "profile": "Profilis", "profit": "<PERSON><PERSON><PERSON>", "profitAmount": "Pelno suma", "profitRatio": "<PERSON><PERSON><PERSON>", "profitTimes": "Pelno kartų", "profitWallet": "<PERSON><PERSON><PERSON>", "progress": "Pažanga", "purchase": "Pirkimas", "purchaseAmountBetween": "Pirk<PERSON> suma turi būti tarp {} ir {}", "purchaseContracts": "Pirkimo k<PERSON>", "purchaseDate": "Pirkimo data", "purchaseList": "Pirkimų sąrašas", "purchasePrice": "<PERSON><PERSON><PERSON> kaina", "purchaseTime": "<PERSON><PERSON><PERSON>", "purchased": "N<PERSON><PERSON><PERSON>", "purchasedContracts": "Nupirkti kontraktai", "purchasedProducts": "<PERSON><PERSON><PERSON><PERSON> produktai", "qrCodeScan": "Prašome nuskenuoti QR kodą", "readLess": "<PERSON><PERSON><PERSON><PERSON>", "readMore": "Daugiau", "readyToUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtime": "<PERSON><PERSON> la<PERSON>", "reason": "Priežastis", "rechargeAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rechargeOrderSubmittedSuccessfully": "<PERSON><PERSON><PERSON><PERSON><PERSON>ėkming<PERSON> pateikt<PERSON>", "rechargeQr": "Papildymo QR kodas", "records": "Įrašai", "referAndEarn": "Rekomenduokite ir uždirbkite", "registerSuccess": "<PERSON><PERSON><PERSON><PERSON> paskyra sukurta, prašome prisijungti iš naujo.", "rejected": "Atmes<PERSON>", "report": "Pranešti apie problemą", "requestSuccess": "Užklausa sėkminga", "resendCode": "Siųsti kodą iš naujo", "reservedPhone": "Rezervuotas telefonas", "resetPassword": "Atkurti slaptažodį", "retracementRate": "<PERSON>k<PERSON><PERSON><PERSON> norma", "retry": "Bandyti dar kartą", "retryUpdate": "Bandyti dar kartą", "returnRate": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "revenue": "Pajamos", "revenueDetails": "Pajamų detalės", "reviewFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pra<PERSON><PERSON> pateikti iš naujo", "search": "Ieškoti JAV akcijų...", "secondGeneration": "Antra karta", "seconds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "secondsUpper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "security": "<PERSON><PERSON><PERSON><PERSON>", "securityOptionsLabel": "Saugumo parink<PERSON>s", "securitySettings": "Saugumo nustatymai", "seeAll": "Žiūrė<PERSON> viską", "seeMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWithdrawalAddress": "Pasirinkti išė<PERSON><PERSON>", "sell": "<PERSON><PERSON><PERSON><PERSON>", "sellDate": "Pardavimo data", "sellPrice": "<PERSON><PERSON><PERSON><PERSON> kaina", "sellQuantity": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "sellTime": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "sellingPrice": "<PERSON><PERSON><PERSON><PERSON> kaina", "sellingTime": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "sendCode": "Siųsti kodą", "sendCodeAlert": "<PERSON><PERSON>ųsta<PERSON> į jū<PERSON> paskyr<PERSON>", "sendCodeToEmail": "Siųsti kodą", "send_code": "Siųsti kodą", "serviceAgreement": "Paslaugų sutartis", "setAmount": "Nustatyti sumą", "setWalletPassword": "Nustatyti piniginės slaptažodį", "settled": "Atsiskaityta", "share": "<PERSON><PERSON><PERSON>", "shareText": "Užregistruokite savo rekomendaciją su žemiau esančia nuoroda", "signIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON><PERSON>", "signinAgree": "Registruodamasis, sutinku su ", "signingYouOut": "<PERSON><PERSON> at<PERSON>...", "singleAmount": "Viena suma", "size": "<PERSON><PERSON><PERSON>", "skip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smallRechargesBelow": "<PERSON><PERSON>i papildymai žemiau $100 nebus įskaitomi", "smartInvestment": "Protinga investicija", "smartInvestmentCycle": "Protingos investicijos ciklas", "smartInvestmentProducts": "Protingos investicijos produktai", "soldOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "somethingWentWrong": "<PERSON><PERSON><PERSON>", "somethingWentWrongTryAgain": "<PERSON><PERSON><PERSON>, prašome bandyti dar kartą", "starMentor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startTime": "<PERSON><PERSON><PERSON><PERSON>", "statistics": "Statistika", "stockCode": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "stockName": "<PERSON><PERSON><PERSON><PERSON>", "stockTrading": "Akcijų prekyba", "stocks": "<PERSON><PERSON><PERSON><PERSON>", "storagePermissionRequired": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON> le<PERSON>", "subject": "<PERSON><PERSON>", "submit": "Pat<PERSON><PERSON><PERSON>", "submitRecharge": "Pateikti papildymą", "submitRequest": "Pateikti užklausą", "successful": "Sėkminga", "successfully": "Sėkmingai", "suffixAddText": "P<PERSON>ė<PERSON> +", "summary": "<PERSON><PERSON><PERSON>", "summaryTransfer": "Ar tikrai norite pervesti šią sumą?", "summaryWithdraw": "Ar tikrai norite išimti š<PERSON>ą sumą?", "support": "<PERSON><PERSON><PERSON><PERSON>", "systemNotifications": "Sistemos <PERSON>", "tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tc": "S&S ", "termsAndConditions": "<PERSON><PERSON><PERSON><PERSON> ir nuo<PERSON>", "theStatisticalSample": "Statist<PERSON><PERSON> imtis yra duomenys, pagrįsti $10000 per pastaruosius 30 dienų.", "thirdGeneration": "Trečia karta", "thirtyMin": "30 minučių", "thisRechargeAddressOneTime": "<PERSON><PERSON> papildymo adresas yra vien<PERSON>", "timesUpper": "Kartų", "toCollectionWallet": " į <PERSON><PERSON>", "toCommunity": "Į bendruomenę", "toCommunityWallet": " į <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toDepositWallet": " į indėlių piniginę", "toProfitWallet": " į pelno piniginę", "todayProfit": "Šiandienos pelnas", "todaysChange": "Šiandienos\n<PERSON>", "todaysStockMarket": "Šiandienos akcijų rinka", "toolTipWarning2": "Kontraktas pasiekė maksimalų veik<PERSON> ir laikinai negali būti įsigytas", "tootTipWarning": "Visada yra rizikų\nsusijusių su investavimu, tod<PERSON><PERSON>\nsvarbu atlikti savo\ntyrimą ir\nsuprasti galimus\ntrūkumus.", "totalAmount": "Bendra suma", "totalMembers": "Viso narių", "totalRevenue": "<PERSON><PERSON> p<PERSON>", "tradingDays": "Prekybos dienos", "tradingWallet": "Prekybos piniginė", "transactionCycle": "Transakcijos c<PERSON>", "transactionHash": "Transakcijos hash", "transactionHashDescription": "🛡️ <PERSON><PERSON><PERSON><PERSON> trans<PERSON> būsen<PERSON> sekim<PERSON> ir patvir<PERSON><PERSON> blokų grandinėje", "transactionHashDescription2": "Galvokite apie tai kaip apie skaitmeninį kvitą jūsų transakcijai.", "transactionRecords": "Transakcijų įrašai", "transactionsHistory": "Transakcijų istorija", "transfer": "<PERSON><PERSON><PERSON>", "transferChargedAmount": "Likutis {} bus apmokestintas 20% mokesčiu ({} iš viso)", "transferExpectedReceive": "<PERSON><PERSON><PERSON><PERSON> gauti suma yra {}", "transferFreeAmount": "<PERSON><PERSON><PERSON> pervesti {} ne<PERSON><PERSON><PERSON><PERSON>", "transferTo": "<PERSON><PERSON><PERSON> ", "transferred": "Pervesta", "trc20": "TRC 20", "trc20_description": "• Always 64 characters (hex format)\n• No 0x at the beginning (e.g., 59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "trc20_title": "✅ TRC20 (TRON network):", "tryAgain": "Bandyti dar kartą", "tutorCommission": "<PERSON><PERSON><PERSON><PERSON>", "type": "Tipas", "typeBack": "atgal", "typeCollection": "rinkimas", "typeConfirmWallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeDeposit": "indėlis", "typeFront": "priekis", "typeGoogle": "google", "typePaymentERC": "ERC20", "typePaymentTRC": "TRC20", "unBindSuccess": "Sėkmingai atsieta", "unbind": "<PERSON><PERSON><PERSON>", "unknown": "Nežinoma", "unknownVersion": "Nežinoma", "up": "Aukštyn", "upcoming": "Art<PERSON><PERSON><PERSON><PERSON>", "updateNow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateRequired": "Re<PERSON>ling<PERSON> at<PERSON>jin<PERSON>", "updating": "Atnaujinama...", "uploadBackIdCard": "Įkelti ID/Paso/Vairuotojo pažymėjimo galinę pusę", "uploadFrontIdCard": "Įkelti ID/Paso/Vairuotojo pažymėjimo priekinę pusę", "uploadIdBack": "Įkelti ID/Paso/Vairuotojo pažymėjimo galinę pusę", "uploadIdCard": "Įkelti ID/Pasą/Vairuotojo p<PERSON>ą", "uploadIdFront": "Įkelti ID/Paso/Vairuotojo pažymėjimo priekinę pusę", "uploadImageError": "<PERSON><PERSON>o dydis per didelis", "urlPrefix": "https://", "usMarket": "JAV rinka", "usdt": "USDT", "user": "Vartotojas", "validAddressNameMsg": "Įveskite galiojantį adreso pavadin<PERSON>ą", "verify": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "Žiūrė<PERSON> viską", "vipLevelError": "Jums reikia pasiekti {} lygį, kad gal<PERSON> pirkti", "vipNotice": "Nepakankamas VIP lygis", "vipNoticeDescription": "Minimalus VIP lygis {} reikalingas sekti šį mentorių", "volume": "<PERSON><PERSON><PERSON><PERSON>", "wallet": "piniginė", "walletAddressExists": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON> jau egzistuo<PERSON>", "walletNameExists": "Adreso pavadinimas jau egzistuoja", "walletPass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "walletPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "walletPasswordShouldBe": "Piniginės slaptažodis turi būti 6 simbolių", "walletUpdatedToast": "Pinigin<PERSON><PERSON>", "warning": "Įspėjimas", "weHaveSent": "Išsiuntėme vienkartinį slaptažodį į jūsų registruotą el. paštą", "week": "Savaitė", "welcome": "Sveiki atvykę!", "whatIsATransactionHash": "Kas yra transak<PERSON> hash (TxHash)?", "whatIsATransactionHashDescription": "Transakcijos hash yra unikalus ID, priskirtas kiekvienai transakcijai blokų grandinėje. Jis atrodo kaip il<PERSON> raidžių ir skaičių eilutė (pvz., 0x59d24d44... arba 59d24d44...).", "whatsappSupport": "WhatsApp pala<PERSON>", "whatsappSupportDesc": "Susisiekite su mumis per WhatsApp", "willBeSentToProfitWallet": "Bus išsiųsta į pelno piniginę", "winRate": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "withdraw": "<PERSON><PERSON><PERSON><PERSON>", "withdrawAddressHintText": "Prašome įvesti išėmimo adresą", "withdrawAddressLabelText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawAddresses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawHistory": "Išėmimo istor<PERSON>", "withdrawInvestment": "<PERSON><PERSON><PERSON><PERSON>", "withdrawLimit": "Negalite išimti mažiau nei $100", "withdrawal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawalAmount": "<PERSON>š<PERSON><PERSON><PERSON> suma", "withdrawalFee": "<PERSON><PERSON><PERSON><PERSON><PERSON> :", "withdrawnProfit": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "workingAge": "<PERSON><PERSON>", "years": "metų", "yearsOfExperience": "Patirties metų", "zeroContractToast": "Nėra galimų kontraktų", "unavailableFunds": "Nepasiekiami <PERSON>", "availableFunds": "<PERSON><PERSON><PERSON>", "welcomeTo": "Sveiki atvykę į SIS", "hi": "Sveiki"}