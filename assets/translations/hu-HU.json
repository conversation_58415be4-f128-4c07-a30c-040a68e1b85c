{"FreezeAmount": "Befagyasztott összeg", "OR": "VAGY", "aDayAgo": "egy nappal e<PERSON>", "aMinuteAgo": "egy perccel e<PERSON>", "aMonthAgo": "egy h<PERSON><PERSON>", "aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarCardUnder": "Személyi igazolvány/útlevél/jogosítvány ellenőrz<PERSON> al<PERSON>ll", "aboutUs": "<PERSON><PERSON><PERSON><PERSON>", "accountInformation": "Fiók információk", "accountInformationStatus1": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> frissítve", "accountInformationStatus2": "A pénztárca jelszava sikeresen beállítva", "accountInformationStatus3": "A telefonszám sikeresen regisztrálva", "accountInformationStatus4": "A Google kód sikeresen létrehozva", "accountName": "Fiók neve", "accountNumber": "Fiókszám", "accountTotal": "Fiók teljes összege", "actualAmount": "Tényleges összeg:", "actualProfit": "Tényleges nyereség", "adAdminWill": "Egy adminisztrátor ellenőrzi az adataidat, ezt követően használhatod a platformot", "addAccountInfo": "Töltsd ki a fiókadataidat", "addAddressWarning": "Csak 6 kifizetési c<PERSON><PERSON>, és ezek nem módosíthatók.", "addWithdrawal": "Kifizetési cím ho<PERSON> +", "addWithdrawalAddress": "Kifizetési cím ho<PERSON>", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalFunds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalInvestment": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>", "address": "Cím", "addressNameTextController": "<PERSON><PERSON><PERSON> neve", "addressTextHintText": "Add meg a cím nevét", "addressTextLabelText": "<PERSON><PERSON><PERSON> neve", "alertCancel": "A törlés megerősítése után továbbra is élvezheted az előnyöket.", "alertMsgAdminVerify": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON><PERSON> be újra.", "alertProceedToLogin": "Tovább a bejelentkezéshez", "alertUnbind": "A leválasztás megerősítése után a teljes szerződés összege 30 munkanapon belül visszautalásra kerül a nyereség pénztárcádba, és az első 15 munkanapig továbbra is kapsz jövedelmet.", "alreadyHaveAccount": "<PERSON><PERSON><PERSON>?", "amount": "Összeg", "amountExceedsMaximumAllowed": "Az összeg meghaladja a maximálisan engedélyezettet", "amountMustBeInUnits": "Az összegnek egységekben kell lennie", "amountMustBeMultipleOf": "Az összegnek a következő többszörösének kell lennie", "anHourAgo": "e<PERSON> <PERSON><PERSON><PERSON><PERSON>", "and": "és", "appName": "SF India: <PERSON><PERSON>", "appUpdateAvailable": "Alkalmazásfrissítés elérhető", "append": "Hozzáadás", "appendRejectedAmount": "Elutasított összeg hozzáadása", "approvedSuccess": "Fiókod jóváhagyva", "atLeast8character": "Legalább 8 karak<PERSON>, tartalmaznia kell <PERSON><PERSON>, legalább egy nagy- és kisbet<PERSON>, és nem tartalmazhat szóközöket", "atLeast8characterWithoutUpperCase": "Legalább 8 karak<PERSON>, tartalmaznia kell <PERSON><PERSON>, e<PERSON> k<PERSON>, és nem tartalmazhat szóközöket", "auditing": "<PERSON><PERSON><PERSON><PERSON>", "authentication": "Hitelesítés", "automaticRenewal": "Automatikus me<PERSON>ú<PERSON>", "available": "Elérhető", "availableBalance": "Elérhető egyenleg", "availableContracts": "Elérhető szerződések", "availableFrom": "Elérhető ettől", "availableUntil": "Elérhető eddig", "backImageLarge": "A hátsó kép mérete túl nagy", "bank": "Bank", "bankAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bankText": "<PERSON><PERSON><PERSON>ts be egy bankszámlát a kifizetéshez", "benefitRules": "Előnyök szabályai", "bindIdCard": "Személyi i<PERSON>vány/útlev<PERSON>l/jogos<PERSON><PERSON><PERSON> hozz<PERSON>apcsolása", "bindMobile": "Mobiltelefonszám hozzákapcsolása", "bonusWallet": "Bónusz pénztárca", "buildYourProfile": "Építsd fel nyereséged", "buy": "Vásárlás", "buyIt": "Vedd meg", "buyPosition": "Pozíció vásárlása", "buyPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buyQuantity": "Vásárolt men<PERSON>ég", "buyingPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buyingTimeNotice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON>, hogy minden nap 14:00 (ET) után a vásárlás már nem elérhető. Köszönjük megértésedet és támogatásodat!", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "cancelSuccess": "Sikeresen törölve", "canceled": "Törölve", "captchaCode": "<PERSON><PERSON>", "celebrityMentor": "<PERSON><PERSON><PERSON> mentor", "certificationCompleted": "A hitelesítés már <PERSON>", "change": "Módosítás", "changeAuthCode": "Google hitelesítési kód módosítása", "changeGoogleAuthentication": "Google hitelesítési kód módosítása", "changeIdentity": "Azonosság módosítása", "changeLoginPassword": "Bejelentkezési jelszó módosítása", "changeNumber": "Telefonszám módosítása", "changePassword": "Jelszó módosítása", "changePaymentPassword": "Fizetési j<PERSON>ó módosítása", "changeWalletPassword": "Pénztárca jelszó módosítása", "chat": "Csevegés", "chats": "Csevegések", "checkBoxSignupError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ho<PERSON> elolvastad és elfogadod a Felhasználási feltételeket és az Adatvédelmi szabályzatot", "checkEmail": "E-mail ellenőrzése", "checkPhone": "Telefon ellenőrzése", "chooseWallet": "Válassz <PERSON>", "close": "Bezárás", "code": "<PERSON><PERSON><PERSON>", "collectionWallet": "Gyűjtő pénztárca", "commission": "<PERSON><PERSON><PERSON><PERSON>", "community": "Közösség", "communityWallet": "Közösségi <PERSON>ztárca", "completed": "Befejezve", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmDelete": "Törlés megerősítése", "confirmHintText": "<PERSON><PERSON><PERSON>ó megerősítése", "confirmInvestment": "Befektetés megerősítése", "confirmPasswordLabel": "<PERSON><PERSON><PERSON>ó megerősítése", "confirmWalletPassword": "Pénztárca jelszó megerősítése", "confirmWalletPasswordLabel": "Pénztárca jelszó megerősítése", "connected": "Csatlakoztatva", "connecting": "Csatlakozás...", "contactSupport": "Kapcsolatfelvétel a támogatással", "continueTxt": "Folytatás", "contractAnnouncement": "Szerződés bejelentés", "contractList": "Szerződések listája", "contractPrice": "Szerződés ára", "contractSize": "Szerződés mérete", "contractType": "Szerződés típusa", "contractType1": "konzervatív", "contractType2": "rob<PERSON><PERSON><PERSON>", "contractType3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contractedAmount": "Szerződött összeg", "contracts": "Szerződések", "copied": "M<PERSON>ol<PERSON>", "copiedClipboard": "Másolva a vágólapra", "copy": "Másolás", "copyCode": "Másold az alábbi kódo<PERSON>", "countryDialCode": "+36", "createTime": "Létrehozás ideje", "crypto": "<PERSON><PERSON><PERSON>", "cumulativeIncome": "Összesített jövedelem", "currentVersion": "<PERSON><PERSON><PERSON><PERSON> verzi<PERSON>", "customValidatorMsg": "több mint 0 karakternek kell lennie", "customerService": "Ügyfélszolgálat", "customerSupport": "Ügyfé<PERSON>", "cycle": "<PERSON><PERSON><PERSON>", "daily": "<PERSON><PERSON>", "darkMode": "<PERSON><PERSON><PERSON><PERSON> mód", "datePrivacy": "Hatálybalépés dátuma: 2023.06.27.", "dateTerms": "Hatálybalépés dátuma: 2023.06.27.", "day": "nap", "days": "nap", "daysAgo": "<PERSON>pal e<PERSON>", "daysOfHolding": "Tartási napok", "daysSinceUnbind": "Napok a leválasztás óta", "deleteAddress": "<PERSON><PERSON><PERSON>", "deleteAddressConfirmation": "Biztosan törölni szeretnéd ezt a címet?", "deposit": "Be<PERSON>ze<PERSON>s", "depositTip": "Mi az a tranzakciós hash (TxHash)?", "depositWallet": "Befizetési pénztárca", "details": "Részletek", "didntRecieve": "<PERSON><PERSON> ka<PERSON>?", "directMembers": "Közvetlen tagok", "done": "<PERSON><PERSON><PERSON>", "dontHaveAccount": "Nincs még fiókod?", "down": "Le", "download": "Letöltés", "downloadComplete": "Letöltés befejezve", "downloadFailed": "Letöltés sikertelen", "downloadUrlNotAvailable": "A letöltési URL nem elérhető", "downloading": "Letöltés...", "downloadingUpdate": "Frissítés letöltése...", "drawdown": "Visszaesés", "driverLicense": "Jogosítvány", "earningsLastDay": "Utolsó napi bevétel", "editBank": "Bankszá<PERSON>la szerkesztése", "email": "E-mail", "emailAlready": "Az e-mail már regis<PERSON><PERSON><PERSON><PERSON><PERSON> van", "emailError": "<PERSON>em t<PERSON> erre az e-mail címre", "emailHintText": "Add meg az e-mailt", "emailLabelText": "E-mail", "emailSupport": "E-mail támogatás", "emailSupportDesc": "<PERSON><PERSON><PERSON>j nekünk e-mailt", "emailValidatorMsg": "Érvénytelen e-mail cím", "emailVerification": "E-mail el<PERSON><PERSON><PERSON><PERSON> kód", "empty": "Még nincs adat", "emptyAddressEmailMsg": "Add meg a kifizetési címeket", "emptyAddressNameMsg": "Add meg a cím nevét", "emptyEmailMsg": "Add meg az e-mailt", "emptyPasswordMsg": "Add meg a j<PERSON>t", "emptyStringMsg": "A mező nem lehet üres", "emptyUserNameMsg": "Add meg a felhasználónevet", "emptyWalletMsg": "Add meg a pénztárca jelszó megerősítését", "emptyWalletPasswordMsg": "Add meg a pénztárca jelszavát", "endTime": "Befejezési idő", "enter4to12characters": "Adj meg 4–12 ka<PERSON><PERSON><PERSON>, szóközök nem megengedettek", "enterAmount": "Add meg az összeget", "enterCaptcha": "Add meg a captcha kódot", "enterCode": "Add meg a kódot", "enterEmailCode": "Add meg az e-mail ellenőrző kódot", "enterExactly6Digits": "Adj meg pontosan 6 számjegyet", "enterGoogleCode": "Add meg a Google hitelesítési kódot", "enterPassword": "Add meg a j<PERSON>t", "enterPhone": "Add meg a telefonszámot", "enterPhoneNumber": "Add meg a telefonszámot", "enterTheCode": "Add meg a kódot", "enterThePassword": "Add meg a jelszavakat", "enterTransactionHash": "Add meg a tranzakciós hash-t", "enterValidErc": "Adj meg érvényes ERC20 címet", "enterValidGoogleCode": "Adj meg érvényes Google kódot", "enterValidPassword": "<PERSON>j meg érv<PERSON> j<PERSON>", "enterValidPhone": "Adj meg érvényes mobiltelefonszámot", "enterValidTransactionHash": "Adj meg érvényes tranzakciós hash-t", "enterValidTrc": "Adj meg érvényes TRC20 címet", "enterValidUsername": "Adj meg érvényes felhasználónevet", "enterVerification": "Add meg az ellenőrző kódot", "enterWalletPassword": "Add meg a pénztárca jelszavát", "enterWithdrawalAmount": "Add meg a kifizetési összeget", "enterYourFriendsEmail": "Add meg barátod e-mail címét", "enter_email": "Add meg az e-mailt", "enter_phone_number": "Add meg a telefonszámot", "erc20": "ERC 20", "erc20_description": "• Mindig 66 karakter (hex formátum)\n• 0x-<PERSON><PERSON> (pl. 0x59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "erc20_title": "✅ ERC20 (ETH/BSC/Polygon stb.):", "error": "Hiba", "errorAuth": "A hitelesí<PERSON>s <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jelent<PERSON>zz be újra", "errorMsg": "Hoppá! Valami elromlott. Próbáld újra később.", "expectedProfit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expert": "Szakértő", "failedToDownloadUpdate": "A frissítés letöltése sikertelen", "failedToInstallUpdate": "A frissítés telepítése sikertelen", "failedToLoadCommissionStats": "A jutalékstatisztika betöltése sikertelen", "fifteenMin": "15 perc", "finance": "Pénzügyek", "finished": "Befejezve", "firstGeneration": "<PERSON><PERSON><PERSON> generá<PERSON>", "fiveMin": "5 perc", "flat": "<PERSON><PERSON><PERSON>", "follow": "Követés", "followPurchaseDetails": "Követési vásárlás részletei", "followPurchaseDetailsTitle": "Követési vásárlás részletei", "followPurchaseRecords": "Követési vásárlási rekordok", "followUpPeriod": "Vásárlási idő", "followUpPurchaseAmount": "Követési vásárlási összeg", "following": "<PERSON><PERSON><PERSON><PERSON>", "forceUpdateRequired": "Frissítés szükséges", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>tted a jelszavad?", "frontImageLarge": "<PERSON><PERSON> kép mérete túl nagy", "fundingThreshold": "Finanszírozási küszöb", "fundingWallet": "Finanszírozási pénztárca", "gainers": "N<PERSON><PERSON><PERSON>", "google": "Google", "googleAuthCode": "Google hitelesítési kód", "googleAuthCodeHintText": "Add meg a Google kódot", "googleAuthCodeLabelText": "Google hitelesítési kód", "googleAuthSubTitle2Text": "Android, iOS - Google Authenticator", "googleAuthSubTitleText": "Töltsd le a Google Authenticator alkalmazást", "googleAuthTitleText": "Kétfaktoros hitelesítés beállítása", "googleAuthentication": "Google hitelesítés", "googleCodeShouldBe": "A Google kódnak 6 karakterből kell állnia", "googleCodeUpdatedToast": "A Google kód frissítve", "handlingFee": "<PERSON><PERSON><PERSON><PERSON> díj", "handlingFeeMessage": "Minden alkalommal kezelési díj szükséges", "haveReadAndAgreedToThe": "Elolvastam és elfogadom", "helpCenter": "Segítségközpont", "high": "Magas", "hintPhone": "xxxxxxxxxx", "hintWallet": "0xxxxx00xx0x00xx0x0x0x0x0x0x0x0x0x", "home": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hotStocks": "Népszerű részvények", "hoursAgo": "<PERSON><PERSON><PERSON><PERSON>", "iAgree": "A regisztrációval elfogadom", "iHaveReadAndAgreed": "Elolvastam és elfogadom", "idCard": "Személyi igazolvány", "idCardBack": "Személyi i<PERSON>vány/útlev<PERSON>l/jogos<PERSON><PERSON><PERSON> h<PERSON>lja", "idCardConfirmation": "Személyi i<PERSON>vány/útlev<PERSON>l/jogos<PERSON><PERSON><PERSON> me<PERSON>ősítése", "idCardFront": "Személyi i<PERSON>vány/útlev<PERSON>l/jogosí<PERSON> eleje", "idCardNumber": "Személyi i<PERSON>vány/útlev<PERSON>l/jogos<PERSON><PERSON><PERSON> s<PERSON>", "idCardNumberErrorMessage": "Adj meg érvényes személyi igazolvány/útlevél/jogosítvány sz<PERSON>mot", "idCardNumberHint": "xxxxxxxxxxxx", "in": "ban/ben", "inProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incorrectCaptcha": "<PERSON><PERSON><PERSON><PERSON>", "incorrectOtp": "Helytelen OTP", "information": "Információ", "initialCapital": "<PERSON>zdeti tőke", "installPermissionRequired": "Telepítési engedély szükséges", "installationComplete": "Telepítés befejezve", "installationFailed": "Telepítés si<PERSON>en", "installing": "Telepítés...", "installingUpdate": "Frissítés telepítése...", "insufficientAvailableBalance": "Elégtelen elérhető egyenleg", "insufficientBalance": "Elégtelen egy<PERSON>leg", "intelligentFollowUpInvestment": "Okos követési befektetés", "interestAmount": "<PERSON>mat <PERSON>", "interestRate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidAmount": "Érvénytelen összeg", "invalidPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidPassword2": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "investmentAmount": "Befektetési összeg", "investmentUnits": "Befektetési egységek", "invitationCode": "<PERSON><PERSON><PERSON><PERSON><PERSON> kód", "invitationCodeError": "A meghívó kód érvénytelen", "invitationCodeSuffix": "?meghívókód=", "invitationDescription": "Fektess be a jövőbe még ma! Töltsd le az SF India: <PERSON><PERSON> alkalmazást és regisztrálj a kezdéshez.", "invitationLinkHintText": "http://supfut.com?meghivokod=abcdefg", "invitationLinkLabelText": "Meghívó link", "jobPosition": "Munkakör", "justNow": "éppen most", "language": "Nyelv választása", "latestPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "latestTransaction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leadingConcept": "Vezető koncepció", "leadingIndustry": "Vezető iparág", "letsStart": "Kezdjünk <PERSON>ki", "liveChat": "<PERSON><PERSON><PERSON> cseve<PERSON>s", "liveChatDesc": "Csevegj a támogatási csapatunkkal", "lockPeriod": "Zárol<PERSON><PERSON> időszak", "lockedBalance": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>leg", "logIn": "Bejelentkezés", "login": "Bejelentkezés", "loginHintText": "Add meg a felhasználónevet", "loginLabelText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loginLinkHintText": "http://supfut.com/login", "loginLinkLabelText": "Bejelentkezési link", "loginLinkSuffix": "/login", "loginPasswordUpdatedToast": "A bejelentkezési jelszó frissítve", "logout": "Kijelentkezés", "logoutConfirmation": "Biztosan ki szeretnél jelentkezni?", "losers": "<PERSON><PERSON>z<PERSON>ek", "lossesTimes": "Veszteségek száma", "low": "Alacsony", "mainstreamCurrency": "Fő\npénznem", "maintenanceNotice": "Karbantartási értesítés", "market": "Piac", "marketOverview": "<PERSON><PERSON><PERSON>", "marketUpdates": "<PERSON><PERSON><PERSON>", "max": "Maximum", "maxDrawdown": "<PERSON><PERSON><PERSON>", "maximumAmount": "A maximális összeg", "maximumPurchaseAmount": "Maxim<PERSON>lis vásárlási összeg", "maximumWithdrawalAmount": "A maximális kifizetési összeg", "memberInfo": "Tag információk", "members": "Tagok", "mentorCommission": "<PERSON><PERSON>", "mentors": "Mentorok", "message": "Üzenet", "minimumAmount": "A minimális összeg", "minimumAmountNotMeet": "A minimális összeg nem teljesül", "minimumPurchaseAmount": "Minimális vásárlási összeg", "minimumWithdrawalAmount": "A minimális kifizetési összeg", "minutesAgo": "percc<PERSON> e<PERSON>", "missions": "Küldetések", "month": "Hónap", "monthly": "<PERSON><PERSON>", "monthlyReturn": "<PERSON><PERSON> ho<PERSON>", "mustBeMultipleOf": "a következő többszörösének kell lennie", "myContracts": "<PERSON><PERSON><PERSON><PERSON> szerződéseim", "name": "Név", "nameValidatorMsg": "Több mint 0 karakternek kell lennie", "networkError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hiba", "networkLineLabelText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newPassword": "Új pénztárca jelszó", "newPhoneNumberLabel": "Új telefonszám", "newWalletPasswordLabel": "Új pénztárca jelszó", "news": "<PERSON><PERSON><PERSON>", "newsUpdates": "Hírfrissítések", "next": "Következő", "noBalanceAvailable": "<PERSON><PERSON><PERSON> egyenleg", "noBalanceInformation": "<PERSON><PERSON><PERSON>r<PERSON>ő egyenleg információ", "noDataAvailable": "<PERSON><PERSON><PERSON> adat", "noDetailsAvailable": "<PERSON><PERSON><PERSON><PERSON>", "noGalleryPermission": "Engedélyezd a fotók hozzáférését a beállításokban a folytatáshoz", "noInternet": "Ellenőrizd a hálózati kapcsolatodat", "noPurchasesYet": "Még nincs v<PERSON>", "noResultsFound": "<PERSON><PERSON><PERSON>", "notAvailable": "<PERSON><PERSON>", "notification": "Értesítések", "numOfContracts": "Szerződések száma", "numberOfTransactions": "Tranzakciók száma", "numberUpdatedToast": "A telefonszám frissítve", "numbering": "Számozás", "officeCompany": "Irodai cég", "ok": "OK", "onBoardScreenSubtitle": "Kezdj el befektetni még ma, és a jövőd jobbra fordul", "oneClickPurchase": "<PERSON><PERSON> kattint<PERSON>os vásárlás", "oneClickSmartInvestmentDescription": "Az egy kattintásos vásárlás rendkívül egységes és könnyen kereskedhető. Az elemzők először lépnek a piacra az intézményi csatornán keresztül. Az egy kattintásos vásárlási időszak alatt nincs szükség semmilyen műveletre. Csak kövesd a műveletet, így kezeid felszabadulnak, hogy ne felejtsd el követni a piacot. A napi nyereségek automatikusan visszaállnak.", "oneClickSmartInvestmentInstructions": "Egy kattintásos vásárlási útmutató", "oneMin": "1 perc", "open": "Megnyitás", "openWallet": "Pénztárca megnyitása", "optional": "Opcionális", "or": "VAGY", "orderDate": "<PERSON><PERSON><PERSON><PERSON>", "orderNo": "Ren<PERSON><PERSON><PERSON><PERSON>", "otherMembers": "<PERSON><PERSON><PERSON><PERSON>", "otp": "E-mail el<PERSON><PERSON><PERSON><PERSON> kód", "otpCodeError": "Adj meg pontosan 6 számjegyet", "otp_phone": "SMS ellenőrző kód", "passport": "Útlevél", "password": "Je<PERSON><PERSON><PERSON>", "passwordHint": "**********", "passwordHintText": "Add meg a j<PERSON>t", "passwordNotEqual": "Add meg a megfelelő jelszót", "passwordUpdatedSuccessfully": "A jelszó si<PERSON>esen frissítve", "passwordUpdatedToast": "A jelszó frissítve", "passwordValidationMsg": "A jelszónak legalább 8 karakterből kell állnia, tartalmaznia kell legalább egy s<PERSON>, valamint nagy- és kisbetűk kombinációját.", "pastEarnings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paymentWallet": "Fizetési pénztárca", "pending": "Függőben", "pending2": "Függőben", "phone": "Telefon", "phoneSupport": "Telefonos támogatás", "phoneSupportDesc": "Hívj minket", "phoneVerification": "Telefon ellenőrzés", "phone_number": "Telefonszám", "platformCommission": "Platform jutalék", "pleaseAcceptTheServiceAgreement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fogadd el a szolgáltatási megállapodást", "pleaseComplete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fejezd be a feltöltést \n 30 percen belül", "pleaseDepositSomeAmountToContinue": "Tölts fel valamennyi összeget a folytatáshoz", "pleaseEnterAValidAmount": "<PERSON>j meg érv<PERSON>yes ö<PERSON>zeget", "pleaseEnterAValidInvestmentAmount": "Adj meg érvényes befektetési összeget", "pleaseSelectAnInvestmentProduct": "Válassz egy befektetési terméket", "pleaseWait": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, várj!", "portfolio": "Portfólió", "preview": "Előnézet", "privacyPolicy": "Adatvé<PERSON><PERSON>", "proceedToLogin": "Tovább a bejelentkezéshez", "processing": "Feldolgozás", "productName": "Termék neve", "products": "Termékek", "profile": "Profil", "profit": "Nyereség", "profitAmount": "Nyereség összege", "profitRatio": "Nyereség <PERSON>", "profitTimes": "Nyereséges alkalmak", "profitWallet": "Nyereség pénztárca", "progress": "<PERSON><PERSON><PERSON><PERSON>", "purchase": "Vásárlás", "purchaseAmountBetween": "A vásárlási összegnek {} és {} között kell lennie", "purchaseContracts": "Szerződések vásárlása", "purchaseDate": "Vásárlási d<PERSON>", "purchaseList": "Vásárlási lista", "purchasePrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "purchaseTime": "Vásárlási idő", "purchased": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purchasedContracts": "Megvásárolt szerződések", "purchasedProducts": "Megvás<PERSON><PERSON><PERSON>", "qrCodeScan": "QR-k<PERSON><PERSON>", "readLess": "<PERSON><PERSON><PERSON><PERSON>", "readMore": "<PERSON><PERSON><PERSON>", "readyToUpdate": "Frissítésre k<PERSON>", "realtime": "<PERSON><PERSON>", "reason": "Ok", "rechargeAddress": "Feltöltési cím", "rechargeOrderSubmittedSuccessfully": "A feltöltési rendelés sikeresen elküldve", "rechargeQr": "Feltöltési QR-kód", "records": "Rekordok", "referAndEarn": "<PERSON><PERSON><PERSON>j meg <PERSON> keress", "registerSuccess": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON><PERSON> be újra.", "rejected": "Elutasítva", "report": "Probléma j<PERSON>", "requestSuccess": "A kérés sikeres", "resendCode": "<PERSON><PERSON><PERSON>", "reservedPhone": "Tartalék telefon", "resetPassword": "Je<PERSON><PERSON><PERSON> v<PERSON>zaállítása", "retracementRate": "Visszahúzás arány", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retryUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnRate": "<PERSON><PERSON>", "revenue": "Bevétel", "revenueDetails": "Bevétel részletei", "reviewFailed": "A felülvizsgálat <PERSON>, küld<PERSON> el újra", "search": "Keresés amerikai részvényekre...", "secondGeneration": "Második generáció", "seconds": "másodperc", "secondsUpper": "Másodperc", "security": "Biztonság", "securityOptionsLabel": "Biztons<PERSON>gi <PERSON>", "securitySettings": "Biztons<PERSON>gi <PERSON>", "seeAll": "Összes megtekintése", "seeMore": "Továbbiak megtekintése", "selectWithdrawalAddress": "Válassz kifizetési címet", "sell": "Eladás", "sellDate": "Elad<PERSON><PERSON> d<PERSON>", "sellPrice": "Eladási ár", "sellQuantity": "Eladási men<PERSON>ég", "sellTime": "Eladási idő", "sellingPrice": "Eladási ár", "sellingTime": "Eladási idő", "sendCode": "<PERSON><PERSON><PERSON>", "sendCodeAlert": "A kód elküldve a fiókodba", "sendCodeToEmail": "<PERSON><PERSON><PERSON>", "send_code": "<PERSON><PERSON><PERSON>", "serviceAgreement": "Szolgáltatási megállapodás", "setAmount": "Összeg beállí<PERSON>a", "setWalletPassword": "Pénztárca j<PERSON><PERSON><PERSON>", "settled": "Kiegyenlítve", "share": "Megosztás", "shareText": "Regisztráld az ajánlásodat az alábbi meghívó <PERSON>", "signIn": "Bejelentkezés", "signUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signinAgree": "A regisztrációval elfogadom", "signingYouOut": "Kijelentkezés...", "singleAmount": "Egyszeri összeg", "size": "<PERSON><PERSON><PERSON>", "skip": "Kiha<PERSON><PERSON>", "skipUpdate": "Kiha<PERSON><PERSON>", "smallRechargesBelow": "100 dollár alatti kis feltöltések nem kerülnek jóváírásra", "smartInvestment": "Okos befektetés", "smartInvestmentCycle": "Okos befektetési ciklus", "smartInvestmentProducts": "Okos befektetési termékek", "soldOut": "Elfogyott", "somethingWentWrong": "<PERSON><PERSON>", "somethingWentWrongTryAgain": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starMentor": "<PERSON><PERSON><PERSON><PERSON> mentor", "startTime": "Kezdési idő", "statistics": "Statisztikák", "stockCode": "Részvénykód", "stockName": "Részvény neve", "stockTrading": "Részvénykereskedelem", "stocks": "Részvények", "storagePermissionRequired": "Tárolási engedély szükséges", "subject": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "submitRecharge": "Feltöltés küldése", "submitRequest": "<PERSON><PERSON><PERSON><PERSON>", "successful": "<PERSON><PERSON><PERSON>", "successfully": "<PERSON><PERSON><PERSON><PERSON>", "suffixAddText": "Hozzáadás +", "summary": "Összegzés", "summaryTransfer": "Biztosan át s<PERSON>etnéd utalni az alábbi összeget?", "summaryWithdraw": "Biztosan ki szeretnéd venni az alábbi ö<PERSON>zeget?", "support": "Támogatás", "systemNotifications": "Rendszerértesítések", "tasks": "Feladatok", "tc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "termsAndConditions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theStatisticalSample": "A statisztikai minta 10 000 dollár adaton alapul az elmúlt 30 napból.", "thirdGeneration": "<PERSON><PERSON><PERSON><PERSON>", "thirtyMin": "30 perc", "thisRechargeAddressOneTime": "Ez a feltöltési cím egyszer használatos", "timesUpper": "Alkalom", "toCollectionWallet": "a gyűjtő pénztárcába", "toCommunity": "A közösségbe", "toCommunityWallet": "a közösségi pénztárcába", "toDepositWallet": "a befizetési pénztárcába", "toProfitWallet": "a nyereség pénztárcába", "todayProfit": "<PERSON>", "todaysChange": "<PERSON>", "todaysStockMarket": "<PERSON>", "toolTipWarning2": "A szerződés elérte a maximális működési méretet, és átmenetileg nem vásárolható", "tootTipWarning": "A befektetés mindig kockázatok<PERSON> jár,\n<PERSON><PERSON><PERSON><PERSON>, hogy\nsaj<PERSON>t kutatást végezz,\nés megértsd a lehetséges\nnegatívumokat.", "totalAmount": "Teljes összeg", "totalMembers": "Tagok teljes s<PERSON>ma", "totalRevenue": "Összes bevétel", "tradingDays": "Keresked<PERSON><PERSON> napok", "tradingWallet": "Kereskedési pénztárca", "transactionCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ciklus", "transactionHash": "<PERSON><PERSON><PERSON><PERSON><PERSON> hash", "transactionHashDescription": "🛡️ A tranzakció állapotának nyomon követésére és ellenőrzésére szolgál a blokkláncon", "transactionHashDescription2": "Tekintsd digitális n<PERSON>ak a tranzakciódhoz.", "transactionRecords": "T<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "transactionsHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "transferChargedAmount": "A fennmaradó {} összeg 20%-os dí<PERSON><PERSON><PERSON> kerül ter<PERSON> (összesen {})", "transferExpectedReceive": "A várhatóan ka<PERSON>t összeg {}", "transferFreeAmount": "{} összeget ingyen átutalhat", "transferTo": "<PERSON><PERSON><PERSON><PERSON>", "transferred": "<PERSON><PERSON><PERSON><PERSON>", "trc20": "TRC 20", "trc20_description": "• Mindig 64 karakter (hex formátum)\n• <PERSON><PERSON> 0x-szel (pl. 59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "trc20_title": "✅ TRC20 (TRON hálózat):", "tryAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tutorCommission": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "typeBack": "<PERSON><PERSON><PERSON><PERSON>", "typeCollection": "gyűjtés", "typeConfirmWallet": "pénztárca megerősítése", "typeDeposit": "befize<PERSON><PERSON>", "typeFront": "<PERSON><PERSON><PERSON>", "typeGoogle": "Google", "typePaymentERC": "ERC20", "typePaymentTRC": "TRC20", "unBindSuccess": "<PERSON><PERSON><PERSON><PERSON>", "unbind": "Leválasztás", "unknown": "Ismeretlen", "unknownVersion": "Ismeretlen", "up": "<PERSON><PERSON>", "upcoming": "Közelgő", "updateNow": "<PERSON><PERSON><PERSON><PERSON><PERSON> most", "updateRequired": "Frissítés szükséges", "updating": "Frissítés...", "uploadBackIdCard": "Töltsd fel a személyi igazolvány/útlevél/jogosí<PERSON><PERSON> h<PERSON>lj<PERSON>t", "uploadFrontIdCard": "Töltsd fel a személyi igazolvány/útlevél/jogosí<PERSON><PERSON> elejét", "uploadIdBack": "Töltsd fel a személyi igazolvány/útlevél/jogosí<PERSON><PERSON> h<PERSON>lj<PERSON>t", "uploadIdCard": "Töltsd fel a személyi igazolványt/útlevelet/jogos<PERSON>", "uploadIdFront": "Töltsd fel a személyi igazolvány/útlevél/jogosí<PERSON><PERSON> elejét", "uploadImageError": "A fájl mérete túl nagy", "urlPrefix": "https://", "usMarket": "Amerikai piac", "usdt": "USDT", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validAddressNameMsg": "<PERSON>j meg érv<PERSON>yes címnevet", "verify": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "Összes megtekintése", "vipLevelError": "{} s<PERSON><PERSON> kell elérned a vásárláshoz", "vipNotice": "Elégtelen VIP szint", "vipNoticeDescription": "<PERSON><PERSON><PERSON> a mentorhoz való követéshez legalább {} VIP szint szükséges", "volume": "Forgalom", "wallet": "Pénztárca", "walletAddressExists": "A kifizetési cím már <PERSON>", "walletNameExists": "A cím neve már <PERSON>", "walletPass": "Pénztárca jelszó", "walletPassword": "Pénztárca jelszó", "walletPasswordShouldBe": "A pénztárca jelszónak 6 karakterből kell állnia", "walletUpdatedToast": "A pénztárca jelszó frissítve", "warning": "Figyelmeztetés", "weHaveSent": "Elküldtük az egyszer használatos kódot a regisztrált e-mail címedre", "week": "<PERSON><PERSON><PERSON>", "welcome": "Üdvözlünk!", "whatIsATransactionHash": "Mi az a tranzakciós hash (TxHash)?", "whatIsATransactionHashDescription": "A tranzakciós hash egy egyedi a<PERSON>ó, amelyet minden blokkláncon végzett tranzakcióhoz rendelnek. Hosszú betű- és számsorozatra hasonlít (pl. 0x59d24d44...).", "whatsappSupport": "WhatsApp támogatás", "whatsappSupportDesc": "Lépj kapcsolatba velünk WhatsApp-on keresztül", "willBeSentToProfitWallet": "A nyereség pénztárcába kerül elküldésre", "winRate": "Nyer<PERSON><PERSON>", "withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawAddressHintText": "Add meg a kifizetési címet", "withdrawAddressLabelText": "Kifizetési cím", "withdrawAddresses": "Kifizetési cí<PERSON>k", "withdrawHistory": "Kifize<PERSON><PERSON>", "withdrawInvestment": "Befektetés kifizetése", "withdrawLimit": "<PERSON><PERSON> ve<PERSON> ki 100 doll<PERSON>r alatt", "withdrawal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawalAmount": "Kifizetési összeg", "withdrawalFee": "Kifizetési díj:", "withdrawnProfit": "<PERSON><PERSON><PERSON>", "workingAge": "<PERSON><PERSON><PERSON>", "years": "év", "yearsOfExperience": "Tapasztalati évek", "zeroContractToast": "Nincsenek elérhető szerződések", "unavailableFunds": "<PERSON><PERSON>", "availableFunds": "Elérhető források", "welcomeTo": "Üdvözöljük a SIS-ben", "hi": "Üdvözöljük"}