#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Unicode symbols
CHECKMARK="✅"
CROSS="❌"
ROCKET="🚀"
GEAR="⚙️"
PACKAGE="📦"
CLOCK="⏱️"
SPARKLES="✨"
APPLE="🍎"

# Global variables for multi-flavor builds
ORIGINAL_PUBSPEC_BACKUP="pubspec.yaml.multi_flavor_backup"
ASSETS_OPTIMIZED=false

# Spinner function
spinner() {
    local pid=$1
    local delay=0.1
    local spinstr='|/-\'
    while [ "$(ps a | awk '{print $1}' | grep $pid)" ]; do
        local temp=${spinstr#?}
        printf " [%c]  " "$spinstr"
        local spinstr=$temp${spinstr%"$temp"}
        sleep $delay
        printf "\b\b\b\b\b\b"
    done
    printf "    \b\b\b\b"
}

# Function to print colored output
print_status() {
    local color=$1
    local symbol=$2
    local message=$3
    echo -e "${color}${symbol} ${message}${NC}"
}

# Function to print section headers
print_header() {
    local message=$1
    echo ""
    echo -e "${PURPLE}════════════════════════════════════════${NC}"
    echo -e "${WHITE}${SPARKLES} $message ${SPARKLES}${NC}"
    echo -e "${PURPLE}════════════════════════════════════════${NC}"
    echo ""
}

# Function to show progress
show_progress() {
    local current=$1
    local total=$2
    local flavor=$3
    local percentage=$((current * 100 / total))
    local filled=$((percentage / 5))
    local empty=$((20 - filled))

    printf "\r${CYAN}Progress: ["
    printf "%*s" $filled | tr ' ' '█'
    printf "%*s" $empty | tr ' ' '░'
    printf "] %d%% (%d/%d) - Building %s${NC}" $percentage $current $total "$flavor"
}

# Function to create initial backup of pubspec.yaml
create_pubspec_backup() {
    if [[ -f "pubspec.yaml" ]] && [[ ! -f "$ORIGINAL_PUBSPEC_BACKUP" ]]; then
        cp "pubspec.yaml" "$ORIGINAL_PUBSPEC_BACKUP"
        print_status $GREEN $CHECKMARK "Created backup of original pubspec.yaml"
    fi
}

# Function to restore pubspec.yaml from backup for next flavor
restore_pubspec_for_next_flavor() {
    if [[ -f "$ORIGINAL_PUBSPEC_BACKUP" ]]; then
        cp "$ORIGINAL_PUBSPEC_BACKUP" "pubspec.yaml"
        print_status $GREEN $CHECKMARK "Restored clean pubspec.yaml for next flavor"
        # Remove any flavor marker file
        if [[ -f ".current_flavor" ]]; then
            rm ".current_flavor"
        fi
    else
        print_status $YELLOW "⚠️" "No backup found to restore from"
    fi
}

# Function to cleanup at script exit
cleanup_and_restore() {
    if [[ -f "$ORIGINAL_PUBSPEC_BACKUP" ]]; then
        cp "$ORIGINAL_PUBSPEC_BACKUP" "pubspec.yaml"
        rm "$ORIGINAL_PUBSPEC_BACKUP"
        print_status $GREEN $CHECKMARK "Restored original pubspec.yaml and cleaned up backup"
    fi

    # Remove any remaining flavor marker file
    if [[ -f ".current_flavor" ]]; then
        rm ".current_flavor"
    fi
}

# Trap to ensure cleanup happens on script exit
trap cleanup_and_restore EXIT

# Check dependencies
check_dependencies() {
    print_status $BLUE $GEAR "Checking dependencies..."

    local missing_deps=()

    if ! command -v fvm &> /dev/null; then
        missing_deps+=("fvm")
    fi

    if ! command -v git &> /dev/null; then
        missing_deps+=("git")
    fi

    # Check if we're on macOS (required for iOS builds)
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_status $RED $CROSS "iOS builds are only supported on macOS"
        exit 1
    fi

    # Check if Xcode is installed
    if ! command -v xcodebuild &> /dev/null; then
        missing_deps+=("xcodebuild (Xcode)")
    fi

    # Check if iOS simulator is available
    if ! xcrun simctl list devices &> /dev/null; then
        print_status $YELLOW "⚠️" "iOS Simulator not available, but continuing..."
    fi

    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_status $RED $CROSS "Missing dependencies: ${missing_deps[*]}"
        echo -e "${YELLOW}Please install the missing dependencies and try again.${NC}"
        echo -e "${YELLOW}For iOS builds, you need:${NC}"
        echo -e "${YELLOW}  - Xcode (from App Store)${NC}"
        echo -e "${YELLOW}  - Xcode Command Line Tools: xcode-select --install${NC}"
        exit 1
    fi

    print_status $GREEN $CHECKMARK "All dependencies found"
}

# Get available flavors from lib/flavors.dart
get_flavors() {
    if [[ -f "lib/flavors.dart" ]]; then
        # Extract flavors from enum in flavors.dart, excluding keywords like 'static'
        ALL_FLAVORS=$(grep -A 10 "enum Flavor" lib/flavors.dart | grep "  [a-z]" | grep -v "static" | sed 's/,//g' | awk '{print $1}' | tr '\n' ' ')
    else
        # Fallback to hardcoded flavors based on project structure
        ALL_FLAVORS="sf_app cfroex ncm sis"
    fi

    # Remove any empty entries and trim whitespace
    ALL_FLAVORS=$(echo $ALL_FLAVORS | tr -s ' ' | sed 's/^ *//;s/ *$//')

    if [[ -z "$ALL_FLAVORS" ]]; then
        print_status $RED $CROSS "No flavors found!"
        exit 1
    fi
}

# Interactive build type selection function
select_build_type() {
    local build_types=("release" "debug")
    local selected_type=0  # Default to release (index 0)
    local current_pos=0
    local total_types=${#build_types[@]}

    # Function to draw the build type selection menu
    draw_build_menu() {
        clear
        print_header "Select Build Type"
        echo -e "${CYAN}Use ↑/↓ to navigate, SPACE to select, ENTER to confirm${NC}"
        echo ""

        for ((i=0; i<total_types; i++)); do
            local prefix=""
            local color=""
            local description=""

            if [[ $i -eq $current_pos ]]; then
                prefix="→ "
                color=$YELLOW
            else
                prefix="  "
                color=$WHITE
            fi

            local checkbox=""
            if [[ $i -eq $selected_type ]]; then
                checkbox="[●]"
            else
                checkbox="[ ]"
            fi

            # Add descriptions for build types
            case ${build_types[i]} in
                "release")
                    description=" - Optimized, smaller size, production ready"
                    ;;
                "debug")
                    description=" - Larger size, includes debug info, for testing"
                    ;;
            esac

            echo -e "${color}${prefix}${checkbox} ${build_types[i]}${description}${NC}"
        done

        echo ""
        echo -e "${BLUE}Selected: ${build_types[selected_type]}${NC}"
    }

    # Main build type selection loop
    while true; do
        draw_build_menu

        # Read key input
        IFS= read -rsn1 key
        key_ascii=$(printf "%d" "'$key" 2>/dev/null || echo "0")

        # Handle different key inputs
        if [[ $key == $'\x1b' ]]; then
            # ESC sequence (arrow keys)
            read -rsn2 key
            case $key in
                '[A')  # Up arrow
                    if [[ $current_pos -gt 0 ]]; then
                        current_pos=$((current_pos - 1))
                    fi
                    ;;
                '[B')  # Down arrow
                    if [[ $current_pos -lt $((total_types - 1)) ]]; then
                        current_pos=$((current_pos + 1))
                    fi
                    ;;
            esac
        elif [[ $key == ' ' ]] || [[ $key_ascii -eq 32 ]]; then
            # Space - select current option
            selected_type=$current_pos
        elif [[ $key == '' ]] || [[ $key == $'\n' ]] || [[ $key == $'\r' ]] || [[ $key_ascii -eq 13 ]] || [[ $key_ascii -eq 10 ]]; then
            # Enter - confirm selection
            break
        elif [[ $key == 'q' ]] || [[ $key == 'Q' ]]; then
            # Quit
            echo -e "${YELLOW}Build cancelled by user.${NC}"
            exit 0
        fi
    done

    # Set global build variables
    BUILD_TYPE=${build_types[selected_type]}
    if [[ "$BUILD_TYPE" == "release" ]]; then
        BUILD_FLAG="--release"
        DEBUG_FLAG="false"
    else
        BUILD_FLAG="--debug"
        DEBUG_FLAG="true"
    fi

    clear
    print_status $GREEN $CHECKMARK "Selected build type: $BUILD_TYPE"
}

# Interactive flavor selection function
select_flavors() {
    local flavors_array=($ALL_FLAVORS)
    local selected=()
    local current_pos=0
    local total_flavors=${#flavors_array[@]}

    # Initialize all flavors as selected by default
    for ((i=0; i<total_flavors; i++)); do
        selected[i]=1
    done

    # Function to draw the selection menu
    draw_menu() {
        clear
        print_header "Select Flavors to Build (iOS)"
        echo -e "${CYAN}Use ↑/↓ to navigate, SPACE to select/deselect, 'a' to select all, 'n' to select none, ENTER to confirm${NC}"
        echo ""

        for ((i=0; i<total_flavors; i++)); do
            local prefix=""
            local color=""

            if [[ $i -eq $current_pos ]]; then
                prefix="→ "
                color=$YELLOW
            else
                prefix="  "
                color=$WHITE
            fi

            local checkbox=""
            if [[ ${selected[i]} -eq 1 ]]; then
                checkbox="[✓]"
            else
                checkbox="[ ]"
            fi

            echo -e "${color}${prefix}${checkbox} ${flavors_array[i]}${NC}"
        done

        echo ""
        echo -e "${BLUE}Selected: $(get_selected_count)/${total_flavors} flavors${NC}"
    }

    # Function to get count of selected flavors
    get_selected_count() {
        local count=0
        for ((i=0; i<total_flavors; i++)); do
            if [[ ${selected[i]} -eq 1 ]]; then
                count=$((count + 1))
            fi
        done
        echo $count
    }

    # Function to select all flavors
    select_all() {
        for ((i=0; i<total_flavors; i++)); do
            selected[i]=1
        done
    }

    # Function to deselect all flavors
    select_none() {
        for ((i=0; i<total_flavors; i++)); do
            selected[i]=0
        done
    }

    # Main selection loop
    while true; do
        draw_menu

        # Read key input with better handling
        IFS= read -rsn1 key

        # Convert key to ASCII value for debugging
        key_ascii=$(printf "%d" "'$key" 2>/dev/null || echo "0")

        # Handle different key inputs
        if [[ $key == $'\x1b' ]]; then
            # ESC sequence (arrow keys)
            read -rsn2 key
            case $key in
                '[A')  # Up arrow
                    if [[ $current_pos -gt 0 ]]; then
                        current_pos=$((current_pos - 1))
                    fi
                    ;;
                '[B')  # Down arrow
                    if [[ $current_pos -lt $((total_flavors - 1)) ]]; then
                        current_pos=$((current_pos + 1))
                    fi
                    ;;
            esac
        elif [[ $key == ' ' ]] || [[ $key_ascii -eq 32 ]]; then
            # Space - toggle selection (handle both regular space and ASCII 32)
            if [[ ${selected[current_pos]} -eq 1 ]]; then
                selected[current_pos]=0
            else
                selected[current_pos]=1
            fi
        elif [[ $key == 'a' ]] || [[ $key == 'A' ]]; then
            # Select all
            select_all
        elif [[ $key == 'n' ]] || [[ $key == 'N' ]]; then
            # Select none
            select_none
        elif [[ $key == '' ]] || [[ $key == $'\n' ]] || [[ $key == $'\r' ]] || [[ $key_ascii -eq 13 ]] || [[ $key_ascii -eq 10 ]]; then
            # Enter - confirm selection (handle multiple enter variations)
            local selected_count=$(get_selected_count)
            if [[ $selected_count -eq 0 ]]; then
                echo -e "${RED}Please select at least one flavor to build.${NC}"
                sleep 2
                continue
            fi
            break
        elif [[ $key == 'q' ]] || [[ $key == 'Q' ]]; then
            # Quit
            echo -e "${YELLOW}Build cancelled by user.${NC}"
            exit 0
        fi
    done

    # Build the selected flavors string
    FLAVORS=""
    for ((i=0; i<total_flavors; i++)); do
        if [[ ${selected[i]} -eq 1 ]]; then
            FLAVORS="$FLAVORS ${flavors_array[i]}"
        fi
    done

    # Trim leading/trailing whitespace
    FLAVORS=$(echo $FLAVORS | sed 's/^ *//;s/ *$//')

    clear
    print_status $GREEN $CHECKMARK "Selected flavors: $FLAVORS"
}

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Change to project root directory
cd "$PROJECT_ROOT" || {
    print_status $RED $CROSS "Error: Could not change to project root directory"
    exit 1
}

# Create output directory
OUTPUT_DIR="build/app/outputs/ipa/flavors"
mkdir -p "$OUTPUT_DIR"

# Start time
START_TIME=$(date +%s)

print_header "Flutter Multi-Flavor IPA Builder"

check_dependencies
get_flavors

# Show interactive build type selection
select_build_type

# Show interactive flavor selection
select_flavors

# Convert flavors string to array for counting
FLAVOR_ARRAY=($FLAVORS)
TOTAL_FLAVORS=${#FLAVOR_ARRAY[@]}

print_status $BLUE $PACKAGE "Total flavors to build: $TOTAL_FLAVORS"
print_status $BLUE $PACKAGE "Build type: $BUILD_TYPE"
print_status $BLUE $PACKAGE "Output directory: $OUTPUT_DIR"

# Get version from pubspec.yaml
VERSION=$(grep "version:" pubspec.yaml | head -1 | awk '{print $2}')
VERSION_NAME=$(echo $VERSION | cut -d'+' -f1)
VERSION_CODE=$(echo $VERSION | cut -d'+' -f2)

print_status $BLUE $PACKAGE "Version: $VERSION_NAME (Build: $VERSION_CODE)"

# Build type is now set by select_build_type function
print_status $GREEN $CHECKMARK "Using $BUILD_TYPE build"

# Ask user about version code inclusion
echo ""
echo -e "${CYAN}Do you want to include version code in IPA names?${NC}"
echo ""
while true; do
    read -p "Include version code in IPA names? (y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        INCLUDE_VERSION_CODE=true
        print_status $GREEN $CHECKMARK "Version code will be included in IPA names"
        break
    elif [[ $REPLY =~ ^[Nn]$ ]]; then
        INCLUDE_VERSION_CODE=false
        print_status $GREEN $CHECKMARK "Version code will be ignored in IPA names"
        break
    else
        echo -e "${RED}Please answer y (yes) or n (no)${NC}"
    fi
done

echo ""

# Create backup of original pubspec.yaml before starting
create_pubspec_backup

# Build counter
BUILD_COUNT=0
SUCCESSFUL_BUILDS=()
FAILED_BUILDS=()

for FLAVOR in $FLAVORS; do
    BUILD_COUNT=$((BUILD_COUNT + 1))
    FLAVOR_START_TIME=$(date +%s)

    show_progress $BUILD_COUNT $TOTAL_FLAVORS $FLAVOR
    echo ""

    print_header "Building $FLAVOR flavor ($BUILD_COUNT/$TOTAL_FLAVORS)"

    # Check if universal optimizer exists and run it, fallback to old optimizer
    if [[ -f "scripts/universal_asset_optimizer.sh" ]]; then
        print_status $YELLOW $GEAR "Optimizing assets for $FLAVOR using universal optimizer..."
        if ./scripts/universal_asset_optimizer.sh $FLAVOR > /dev/null 2>&1; then
            print_status $GREEN $CHECKMARK "Assets optimized successfully (universal)"
            ASSETS_OPTIMIZED=true
        else
            print_status $YELLOW "⚠️" "Universal asset optimization failed, trying fallback..."
            # Fallback to old optimizer
            if [[ -f "scripts/optimize_assets.sh" ]] && ./scripts/optimize_assets.sh $FLAVOR > /dev/null 2>&1; then
                print_status $GREEN $CHECKMARK "Assets optimized successfully (fallback)"
                ASSETS_OPTIMIZED=true
            else
                print_status $YELLOW "⚠️" "Asset optimization failed, continuing anyway..."
            fi
        fi
    elif [[ -f "scripts/optimize_assets.sh" ]]; then
        print_status $YELLOW $GEAR "Optimizing assets for $FLAVOR using legacy optimizer..."
        if ./scripts/optimize_assets.sh $FLAVOR > /dev/null 2>&1; then
            print_status $GREEN $CHECKMARK "Assets optimized successfully (legacy)"
            ASSETS_OPTIMIZED=true
        else
            print_status $YELLOW "⚠️" "Asset optimization failed, continuing anyway..."
        fi
    else
        print_status $YELLOW "⚠️" "No asset optimizer found, skipping..."
    fi

    # Clear previous IPA files to ensure we get the correct one
    BUILD_IPA_DIR="build/ios/ipa"
    if [[ -d "$BUILD_IPA_DIR" ]]; then
        print_status $YELLOW "🧹" "Clearing previous IPA files..."
        rm -f "$BUILD_IPA_DIR"/*.ipa 2>/dev/null || true
    fi

    # Build IPA
    print_status $YELLOW $ROCKET "Building IPA for $FLAVOR..."
    BUILD_CMD="fvm flutter build ipa --flavor $FLAVOR --release --dart-define=DEBUG=$DEBUG_FLAG --export-options-plist=ios/ExportOptions.plist"
    echo -e "${CYAN}Running: $BUILD_CMD${NC}"

    if eval "$BUILD_CMD" > "build_${FLAVOR}_ios.log" 2>&1; then
        print_status $GREEN $CHECKMARK "IPA built successfully"
    else
        print_status $RED $CROSS "IPA build failed! Check build_${FLAVOR}_ios.log for details"
        FAILED_BUILDS+=($FLAVOR)
        continue
    fi

    # Restore assets if script exists
    if [[ -f "scripts/restore_assets.sh" ]]; then
        print_status $YELLOW $GEAR "Restoring assets..."
        if ./scripts/restore_assets.sh > /dev/null 2>&1; then
            print_status $GREEN $CHECKMARK "Assets restored successfully"
        else
            print_status $YELLOW "⚠️" "Asset restoration failed, continuing anyway..."
        fi
    fi

    # Find and copy IPA file
    # Flutter names the IPA based on the app's display name, not "Runner.ipa"
    # So we need to find the actual IPA file in the build/ios/ipa directory
    BUILD_IPA_DIR="build/ios/ipa"
    # Set suffix based on flavor and build type
    if [[ "$FLAVOR" == "sf_app" ]]; then
        SUFFIX="TEST"
    else
        if [[ "$BUILD_TYPE" == "debug" ]]; then
            SUFFIX="TEST"
        else
            SUFFIX="RELEASE"
        fi
    fi
    # Build IPA name based on user choice
    if [[ "$INCLUDE_VERSION_CODE" == true ]]; then
        NEW_IPA_NAME="${FLAVOR}_${VERSION_NAME}+${VERSION_CODE}_${SUFFIX}.ipa"
    else
        NEW_IPA_NAME="${FLAVOR}_${VERSION_NAME}_${SUFFIX}.ipa"
    fi

    # Find the IPA file (should be the only .ipa file in the directory)
    IPA_FILE=$(find "$BUILD_IPA_DIR" -name "*.ipa" -type f | head -1)

    if [[ -n "$IPA_FILE" && -f "$IPA_FILE" ]]; then
        print_status $GREEN $PACKAGE "Copying IPA to output directory..."
        cp "$IPA_FILE" "$OUTPUT_DIR/$NEW_IPA_NAME"

        # Get file size
        FILE_SIZE=$(ls -lh "$OUTPUT_DIR/$NEW_IPA_NAME" | awk '{print $5}')
        IPA_FILENAME=$(basename "$IPA_FILE")
        print_status $GREEN $CHECKMARK "IPA saved: $NEW_IPA_NAME ($FILE_SIZE)"
        print_status $BLUE "📱" "Original IPA name: $IPA_FILENAME"
        SUCCESSFUL_BUILDS+=($FLAVOR)
    else
        print_status $RED $CROSS "Error: No IPA file found in $BUILD_IPA_DIR"
        # List contents for debugging
        print_status $YELLOW "🔍" "Contents of $BUILD_IPA_DIR:"
        ls -la "$BUILD_IPA_DIR" 2>/dev/null || echo "Directory not found"
        FAILED_BUILDS+=($FLAVOR)
    fi

    # Calculate build time
    FLAVOR_END_TIME=$(date +%s)
    FLAVOR_DURATION=$((FLAVOR_END_TIME - FLAVOR_START_TIME))
    print_status $BLUE $CLOCK "Build time: ${FLAVOR_DURATION}s"

    # Restore pubspec.yaml for next flavor (except for the last flavor)
    if [[ $BUILD_COUNT -lt $TOTAL_FLAVORS ]]; then
        print_status $YELLOW $GEAR "Preparing for next flavor..."
        restore_pubspec_for_next_flavor
    fi

    echo ""
done

# Final summary
END_TIME=$(date +%s)
TOTAL_DURATION=$((END_TIME - START_TIME))

print_header "Build Summary"

print_status $BLUE $CLOCK "Total build time: ${TOTAL_DURATION}s"
print_status $GREEN $CHECKMARK "Successful builds: ${#SUCCESSFUL_BUILDS[@]}/${TOTAL_FLAVORS}"

if [[ ${#SUCCESSFUL_BUILDS[@]} -gt 0 ]]; then
    echo -e "${GREEN}✅ Successfully built flavors:${NC}"
    for flavor in "${SUCCESSFUL_BUILDS[@]}"; do
        echo -e "   ${GREEN}• $flavor${NC}"
    done
fi

if [[ ${#FAILED_BUILDS[@]} -gt 0 ]]; then
    echo -e "${RED}❌ Failed builds:${NC}"
    for flavor in "${FAILED_BUILDS[@]}"; do
        echo -e "   ${RED}• $flavor${NC}"
    done
    echo -e "${YELLOW}Check the build_*_ios.log files for error details.${NC}"
fi

if [[ ${#SUCCESSFUL_BUILDS[@]} -eq $TOTAL_FLAVORS ]]; then
    print_status $GREEN $SPARKLES "All IPAs built successfully!"
else
    print_status $YELLOW "⚠️" "Some builds failed. Check the logs for details."
fi

print_status $BLUE $PACKAGE "IPAs are available in: $OUTPUT_DIR"

# Open output directory (macOS)
if command -v open &> /dev/null; then
    print_status $BLUE "📂" "Opening output directory..."
    open "$OUTPUT_DIR"
fi

echo ""
print_status $GREEN $SPARKLES "Build process completed!"
