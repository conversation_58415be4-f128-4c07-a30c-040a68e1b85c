# Flavor Assets Configuration
# This file defines which asset directories should be included/excluded for each flavor
# The build scripts will automatically read this configuration

flavors:
  sf_app:
    name: "Super Future"
    include_assets:
      - "assets/"
      - "assets/images/"
      - "assets/icons/"
      - "assets/svg/"
      - "assets/svg/crypto/"
      - "assets/html/"
      - "assets/translations/"
      - "assets/flags/"
      - "assets/logo/"
      - "assets/logo/sf_app/"
      - "assets/splash/"
      - "assets/splash/sf_app/"
    exclude_assets:
      - "assets/logo/cfroex/"
      - "assets/logo/ncm/"
      - "assets/logo/sis/"
      - "assets/splash/cfroex/"
      - "assets/splash/ncm/"
      - "assets/splash/sis/"
      - "assets/svg/cfroex/"
      - "assets/svg/ncm/"
      - "assets/svg/sis/"
      - "assets/icons/cfroex/"
      - "assets/icons/ncm/"
      - "assets/icons/sis/"
      - "assets/images/cfroex/"
      - "assets/images/ncm/"
      - "assets/images/sis/"

  cfroex:
    name: "Forex Fusion"
    include_assets:
      - "assets/"
      - "assets/images/"
      - "assets/icons/"
      - "assets/svg/"
      - "assets/svg/crypto/"
      - "assets/html/"
      - "assets/translations/"
      - "assets/flags/"
      - "assets/logo/"
      - "assets/logo/cfroex/"
      - "assets/splash/"
      - "assets/splash/cfroex/"
      - "assets/svg/cfroex/"
      - "assets/icons/cfroex/"
      - "assets/images/cfroex/"
    exclude_assets:
      - "assets/logo/sf_app/"
      - "assets/logo/ncm/"
      - "assets/logo/sis/"
      - "assets/splash/sf_app/"
      - "assets/splash/ncm/"
      - "assets/splash/sis/"
      - "assets/svg/sf_app/"
      - "assets/svg/ncm/"
      - "assets/svg/sis/"
      - "assets/icons/sf_app/"
      - "assets/icons/ncm/"
      - "assets/icons/sis/"
      - "assets/images/sf_app/"
      - "assets/images/ncm/"
      - "assets/images/sis/"

  ncm:
    name: "FBD"
    include_assets:
      - "assets/"
      - "assets/images/"
      - "assets/icons/"
      - "assets/svg/"
      - "assets/svg/crypto/"
      - "assets/html/"
      - "assets/translations/"
      - "assets/flags/"
      - "assets/logo/"
      - "assets/logo/ncm/"
      - "assets/splash/"
      - "assets/splash/ncm/"
      - "assets/svg/ncm/"
      - "assets/icons/ncm/"
      - "assets/images/ncm/"
    exclude_assets:
      - "assets/logo/sf_app/"
      - "assets/logo/cfroex/"
      - "assets/logo/sis/"
      - "assets/splash/sf_app/"
      - "assets/splash/cfroex/"
      - "assets/splash/sis/"
      - "assets/svg/sf_app/"
      - "assets/svg/cfroex/"
      - "assets/svg/sis/"
      - "assets/icons/sf_app/"
      - "assets/icons/cfroex/"
      - "assets/icons/sis/"
      - "assets/images/sf_app/"
      - "assets/images/cfroex/"
      - "assets/images/sis/"

  sis:
    name: "SIS"
    include_assets:
      - "assets/"
      - "assets/images/"
      - "assets/icons/"
      - "assets/svg/"
      - "assets/svg/crypto/"
      - "assets/html/"
      - "assets/translations/"
      - "assets/flags/"
      - "assets/logo/"
      - "assets/logo/sis/"
      - "assets/splash/"
      - "assets/splash/sis/"
      - "assets/svg/sis/"
      - "assets/icons/sis/"
      - "assets/images/sis/"
    exclude_assets:
      - "assets/logo/sf_app/"
      - "assets/logo/cfroex/"
      - "assets/logo/ncm/"
      - "assets/splash/sf_app/"
      - "assets/splash/cfroex/"
      - "assets/splash/ncm/"
      - "assets/svg/sf_app/"
      - "assets/svg/cfroex/"
      - "assets/svg/ncm/"
      - "assets/icons/sf_app/"
      - "assets/icons/cfroex/"
      - "assets/icons/ncm/"
      - "assets/images/sf_app/"
      - "assets/images/cfroex/"
      - "assets/images/ncm/"

# Global settings
settings:
  # Automatically detect new flavor directories
  auto_detect_flavors: true
  
  # Base asset directories that are always included
  base_assets:
    - "assets/"
    - "assets/images/"
    - "assets/icons/"
    - "assets/svg/"
    - "assets/svg/crypto/"
    - "assets/html/"
    - "assets/translations/"
    - "assets/flags/"
    - "assets/logo/"
    - "assets/splash/"
  
  # Flavor-specific directory patterns
  flavor_patterns:
    - "assets/logo/{flavor}/"
    - "assets/splash/{flavor}/"
    - "assets/svg/{flavor}/"
    - "assets/icons/{flavor}/"
    - "assets/images/{flavor}/"
