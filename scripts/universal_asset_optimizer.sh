#!/bin/bash

# universal_asset_optimizer.sh - Universal script for flavor-specific asset optimization
# Usage: ./scripts/universal_asset_optimizer.sh [flavor_name]
#
# This script automatically:
# 1. Reads flavor configuration from flavor_assets_config.yaml
# 2. Detects all available flavors and their assets
# 3. Optimizes pubspec.yaml for the specified flavor
# 4. Handles any new asset directories automatically
# 5. Provides comprehensive logging and error handling

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Unicode symbols
CHECKMARK="✅"
CROSS="❌"
GEAR="⚙️"
PACKAGE="📦"
ROCKET="🚀"
MAGNIFYING_GLASS="🔍"

# Function to print colored output
print_status() {
    local color=$1
    local symbol=$2
    local message=$3
    echo -e "${color}${symbol} ${message}${NC}"
}

# Function to print header
print_header() {
    local message=$1
    echo ""
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}${message}${NC}"
    echo -e "${CYAN}================================${NC}"
}

# Check if flavor argument is provided
if [ -z "$1" ]; then
    print_status $RED $CROSS "Error: Please provide a flavor name as an argument"
    echo "Usage: ./scripts/universal_asset_optimizer.sh [flavor_name]"
    
    # Auto-detect available flavors
    print_status $BLUE $MAGNIFYING_GLASS "Auto-detecting available flavors..."
    DETECTED_FLAVORS=()
    
    # Check flavorizr.yaml for flavors
    if [ -f "flavorizr.yaml" ]; then
        DETECTED_FLAVORS+=($(grep -E "^  [a-zA-Z_][a-zA-Z0-9_]*:" flavorizr.yaml | sed 's/://g' | sed 's/^  //g'))
    fi
    
    # Check asset directories for flavors
    if [ -d "assets/logo" ]; then
        for dir in assets/logo/*/; do
            if [ -d "$dir" ]; then
                flavor=$(basename "$dir")
                if [[ ! " ${DETECTED_FLAVORS[@]} " =~ " ${flavor} " ]]; then
                    DETECTED_FLAVORS+=("$flavor")
                fi
            fi
        done
    fi
    
    if [ ${#DETECTED_FLAVORS[@]} -gt 0 ]; then
        echo "Available flavors: ${DETECTED_FLAVORS[*]}"
    else
        echo "No flavors detected. Please check your project configuration."
    fi
    
    exit 1
fi

FLAVOR="$1"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
CONFIG_FILE="$SCRIPT_DIR/flavor_assets_config.yaml"

print_header "Universal Asset Optimizer for Flavor: $FLAVOR"

# Change to project root directory
cd "$PROJECT_ROOT" || {
    print_status $RED $CROSS "Error: Could not change to project root directory"
    exit 1
}

# Function to auto-detect flavor assets
auto_detect_flavor_assets() {
    local flavor=$1
    local assets=()
    
    # Base assets that are always included
    assets+=("assets/")
    assets+=("assets/images/")
    assets+=("assets/icons/")
    assets+=("assets/svg/")
    assets+=("assets/svg/crypto/")
    assets+=("assets/html/")
    assets+=("assets/translations/")
    assets+=("assets/flags/")
    assets+=("assets/logo/")
    assets+=("assets/splash/")
    
    # Add flavor-specific assets if they exist
    local flavor_dirs=(
        "assets/logo/$flavor/"
        "assets/splash/$flavor/"
        "assets/svg/$flavor/"
        "assets/icons/$flavor/"
        "assets/images/$flavor/"
    )
    
    for dir in "${flavor_dirs[@]}"; do
        if [ -d "$dir" ]; then
            assets+=("$dir")
            print_status $GREEN $CHECKMARK "Found flavor-specific directory: $dir"
        fi
    done
    
    printf '%s\n' "${assets[@]}"
}

# Function to get excluded assets for a flavor
get_excluded_assets() {
    local flavor=$1
    local excluded=()
    
    # Get all flavor directories
    local all_flavors=()
    if [ -d "assets/logo" ]; then
        for dir in assets/logo/*/; do
            if [ -d "$dir" ]; then
                local f=$(basename "$dir")
                if [[ ! " ${all_flavors[@]} " =~ " ${f} " ]]; then
                    all_flavors+=("$f")
                fi
            fi
        done
    fi
    
    # Exclude other flavors' assets
    for other_flavor in "${all_flavors[@]}"; do
        if [ "$other_flavor" != "$flavor" ]; then
            excluded+=("assets/logo/$other_flavor/")
            excluded+=("assets/splash/$other_flavor/")
            excluded+=("assets/svg/$other_flavor/")
            excluded+=("assets/icons/$other_flavor/")
            excluded+=("assets/images/$other_flavor/")
        fi
    done
    
    printf '%s\n' "${excluded[@]}"
}

# Auto-detect assets for the specified flavor
print_status $BLUE $MAGNIFYING_GLASS "Auto-detecting assets for flavor: $FLAVOR"

# Validate that the flavor exists
FLAVOR_EXISTS=false
if [ -d "assets/logo/$FLAVOR" ] || [ -d "assets/splash/$FLAVOR" ]; then
    FLAVOR_EXISTS=true
fi

# Also check flavorizr.yaml
if [ -f "flavorizr.yaml" ] && grep -q "^  $FLAVOR:" flavorizr.yaml; then
    FLAVOR_EXISTS=true
fi

if [ "$FLAVOR_EXISTS" = false ]; then
    print_status $RED $CROSS "Error: Flavor '$FLAVOR' not found"
    print_status $YELLOW "⚠️" "Please ensure the flavor exists in flavorizr.yaml or has asset directories"
    exit 1
fi

# Get assets to include and exclude
INCLUDE_ASSETS=($(auto_detect_flavor_assets "$FLAVOR"))
EXCLUDE_ASSETS=($(get_excluded_assets "$FLAVOR"))

print_status $GREEN $CHECKMARK "Detected ${#INCLUDE_ASSETS[@]} asset directories to include"
print_status $YELLOW "⚠️" "Detected ${#EXCLUDE_ASSETS[@]} asset directories to exclude"

# Store the current flavor for reference
FLAVOR_MARKER_FILE=".current_flavor"
echo "$FLAVOR" > "$FLAVOR_MARKER_FILE"
print_status $BLUE $GEAR "Stored current flavor ($FLAVOR) in $FLAVOR_MARKER_FILE"

# Optimize pubspec.yaml
PUBSPEC_FILE="pubspec.yaml"
TEMP_PUBSPEC="pubspec.yaml.temp"

print_status $BLUE $GEAR "Optimizing pubspec.yaml for flavor: $FLAVOR"

# Create optimized pubspec.yaml
{
    in_assets_section=false
    while IFS= read -r line; do
        # Check if we're entering the assets section
        if [[ "$line" =~ ^[[:space:]]*assets: ]]; then
            in_assets_section=true
            echo "$line"
            continue
        fi

        # If we're in the assets section, filter assets
        if [ "$in_assets_section" = true ]; then
            # Check if we're leaving the assets section (next section starts)
            if [[ "$line" =~ ^[[:space:]]*[a-zA-Z_][a-zA-Z0-9_]*: && ! "$line" =~ ^[[:space:]]*- ]]; then
                in_assets_section=false
                echo "$line"
                continue
            fi

            # Process asset lines
            if [[ "$line" =~ ^[[:space:]]*- ]]; then
                # Extract the asset path
                asset_path=$(echo "$line" | sed 's/^[[:space:]]*-[[:space:]]*//' | sed 's/"//g')

                # Check if this asset should be excluded
                should_exclude=false
                for exclude_path in "${EXCLUDE_ASSETS[@]}"; do
                    if [[ "$asset_path" == "$exclude_path" ]]; then
                        should_exclude=true
                        print_status $YELLOW "⚠️" "Excluding: $asset_path"
                        break
                    fi
                done

                # Include the asset if it's not excluded
                if [ "$should_exclude" = false ]; then
                    echo "$line"
                fi
                continue
            fi
        fi

        # Output the line
        echo "$line"
    done < "$PUBSPEC_FILE"
} > "$TEMP_PUBSPEC"

# Replace the original pubspec.yaml with the optimized one
mv "$TEMP_PUBSPEC" "$PUBSPEC_FILE"

print_status $GREEN $CHECKMARK "pubspec.yaml optimized for flavor '$FLAVOR'"

# Show optimization summary
print_status $BLUE $PACKAGE "Optimization Summary:"
echo "  • Flavor: $FLAVOR"
echo "  • Assets included: ${#INCLUDE_ASSETS[@]} directories"
echo "  • Assets excluded: ${#EXCLUDE_ASSETS[@]} directories"

# Show included assets
echo ""
echo "📁 Included asset directories:"
for asset in "${INCLUDE_ASSETS[@]}"; do
    if [ -d "$asset" ]; then
        size=$(du -sh "$asset" 2>/dev/null | cut -f1 || echo "N/A")
        echo "  ✅ $asset ($size)"
    else
        echo "  ⚠️  $asset (not found)"
    fi
done

# Show excluded assets
if [ ${#EXCLUDE_ASSETS[@]} -gt 0 ]; then
    echo ""
    echo "🚫 Excluded asset directories:"
    for asset in "${EXCLUDE_ASSETS[@]}"; do
        if [ -d "$asset" ]; then
            size=$(du -sh "$asset" 2>/dev/null | cut -f1 || echo "N/A")
            echo "  ❌ $asset ($size)"
        fi
    done
fi

print_status $GREEN $CHECKMARK "Universal asset optimization completed for flavor: $FLAVOR"
echo ""
echo "Next steps:"
echo "  1. Build your app: flutter build apk --flavor $FLAVOR"
echo "  2. After building, restore assets: ./scripts/restore_assets.sh"
echo ""
