#!/bin/bash

# setup_flavor_assets.sh - Automatically setup flavor-specific asset directories
# Usage: ./scripts/setup_flavor_assets.sh [flavor_name] [--copy-from=source_flavor]
#
# This script:
# 1. Creates all necessary flavor-specific asset directories
# 2. Optionally copies assets from another flavor as a starting point
# 3. Updates pubspec.yaml to include the new directories
# 4. Provides guidance on next steps

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Unicode symbols
CHECKMARK="✅"
CROSS="❌"
GEAR="⚙️"
PACKAGE="📦"
FOLDER="📁"
COPY="📋"

# Function to print colored output
print_status() {
    local color=$1
    local symbol=$2
    local message=$3
    echo -e "${color}${symbol} ${message}${NC}"
}

# Function to print header
print_header() {
    local message=$1
    echo ""
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}${message}${NC}"
    echo -e "${CYAN}================================${NC}"
}

# Parse arguments
FLAVOR=""
COPY_FROM=""

for arg in "$@"; do
    case $arg in
        --copy-from=*)
            COPY_FROM="${arg#*=}"
            shift
            ;;
        *)
            if [ -z "$FLAVOR" ]; then
                FLAVOR="$arg"
            fi
            shift
            ;;
    esac
done

# Check if flavor argument is provided
if [ -z "$FLAVOR" ]; then
    print_status $RED $CROSS "Error: Please provide a flavor name as an argument"
    echo "Usage: ./scripts/setup_flavor_assets.sh [flavor_name] [--copy-from=source_flavor]"
    echo ""
    echo "Examples:"
    echo "  ./scripts/setup_flavor_assets.sh new_flavor"
    echo "  ./scripts/setup_flavor_assets.sh new_flavor --copy-from=sis"
    exit 1
fi

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

print_header "Setting up Flavor Assets for: $FLAVOR"

# Change to project root directory
cd "$PROJECT_ROOT" || {
    print_status $RED $CROSS "Error: Could not change to project root directory"
    exit 1
}

# Define asset directories to create
ASSET_DIRS=(
    "assets/logo/$FLAVOR"
    "assets/splash/$FLAVOR"
    "assets/svg/$FLAVOR"
    "assets/icons/$FLAVOR"
    "assets/images/$FLAVOR"
)

# Create asset directories
print_status $BLUE $GEAR "Creating flavor-specific asset directories..."

for dir in "${ASSET_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_status $GREEN $CHECKMARK "Created directory: $dir"
    else
        print_status $YELLOW "⚠️" "Directory already exists: $dir"
    fi
done

# Copy assets from source flavor if specified
if [ -n "$COPY_FROM" ]; then
    print_status $BLUE $COPY "Copying assets from flavor: $COPY_FROM"
    
    SOURCE_DIRS=(
        "assets/logo/$COPY_FROM"
        "assets/splash/$COPY_FROM"
        "assets/svg/$COPY_FROM"
        "assets/icons/$COPY_FROM"
        "assets/images/$COPY_FROM"
    )
    
    for i in "${!SOURCE_DIRS[@]}"; do
        source_dir="${SOURCE_DIRS[$i]}"
        target_dir="${ASSET_DIRS[$i]}"
        
        if [ -d "$source_dir" ]; then
            if [ "$(ls -A "$source_dir" 2>/dev/null)" ]; then
                cp -r "$source_dir"/* "$target_dir/"
                print_status $GREEN $CHECKMARK "Copied assets from $source_dir to $target_dir"
            else
                print_status $YELLOW "⚠️" "Source directory is empty: $source_dir"
            fi
        else
            print_status $YELLOW "⚠️" "Source directory not found: $source_dir"
        fi
    done
fi

# Update pubspec.yaml to include new asset directories
print_status $BLUE $GEAR "Updating pubspec.yaml to include new asset directories..."

PUBSPEC_FILE="pubspec.yaml"
TEMP_PUBSPEC="pubspec.yaml.temp"

# Check if pubspec.yaml exists
if [ ! -f "$PUBSPEC_FILE" ]; then
    print_status $RED $CROSS "Error: pubspec.yaml not found"
    exit 1
fi

# Add new asset directories to pubspec.yaml if they don't exist
{
    in_assets_section=false
    assets_added=false
    
    while IFS= read -r line; do
        echo "$line"
        
        # Check if we're in the assets section
        if [[ "$line" =~ ^[[:space:]]*assets: ]]; then
            in_assets_section=true
            continue
        fi
        
        # If we're in the assets section and this is the last asset line
        if [ "$in_assets_section" = true ]; then
            # Check if we're leaving the assets section
            if [[ "$line" =~ ^[[:space:]]*[a-zA-Z_][a-zA-Z0-9_]*: && ! "$line" =~ ^[[:space:]]*- ]]; then
                # Add our new asset directories before leaving the section
                if [ "$assets_added" = false ]; then
                    for dir in "${ASSET_DIRS[@]}"; do
                        echo "    - $dir/"
                    done
                    assets_added=true
                fi
                in_assets_section=false
            fi
        fi
    done < "$PUBSPEC_FILE"
    
    # If we never left the assets section, add the directories at the end
    if [ "$in_assets_section" = true ] && [ "$assets_added" = false ]; then
        for dir in "${ASSET_DIRS[@]}"; do
            echo "    - $dir/"
        done
    fi
} > "$TEMP_PUBSPEC"

# Replace the original pubspec.yaml
mv "$TEMP_PUBSPEC" "$PUBSPEC_FILE"
print_status $GREEN $CHECKMARK "Updated pubspec.yaml with new asset directories"

# Show summary
print_status $BLUE $PACKAGE "Setup Summary:"
echo "  • Flavor: $FLAVOR"
echo "  • Directories created: ${#ASSET_DIRS[@]}"
if [ -n "$COPY_FROM" ]; then
    echo "  • Assets copied from: $COPY_FROM"
fi

echo ""
echo "📁 Created asset directories:"
for dir in "${ASSET_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        file_count=$(find "$dir" -type f 2>/dev/null | wc -l)
        echo "  ✅ $dir ($file_count files)"
    fi
done

print_status $GREEN $CHECKMARK "Flavor asset setup completed for: $FLAVOR"
echo ""
echo "Next steps:"
echo "  1. Add your flavor-specific assets to the created directories"
echo "  2. Update flavorizr.yaml to include the new flavor configuration"
echo "  3. Test the build: ./scripts/universal_asset_optimizer.sh $FLAVOR"
echo "  4. Build your app: flutter build apk --flavor $FLAVOR"
echo ""
echo "Asset directories ready for customization:"
for dir in "${ASSET_DIRS[@]}"; do
    echo "  📁 $dir/"
done
