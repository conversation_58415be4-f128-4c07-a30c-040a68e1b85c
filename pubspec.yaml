name: sf_app
description: "A new Flutter project."

publish_to: "none"

version: 1.0.27+50
environment:
  sdk: ^3.5.4

dependencies:
  action_slider: ^0.7.0
  app_links: ^6.3.2
  auto_size_text: ^3.0.0
  bloc: ^8.1.4
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  change_app_package_name: ^1.5.0
  dio: ^5.7.0
  dio_smart_retry: null
  install_plugin: ^2.1.0
  permission_handler: ^11.3.1
  easy_localization: ^3.0.7
  equatable: ^2.0.7
  firebase_core: ^3.4.0
  fl_chart: ^0.69.1
  flutter:
    sdk: flutter
  flutter_avif: ^2.5.0
  flutter_bloc: ^8.1.6
  flutter_bounceable: ^1.1.0
  flutter_flavorizr: ^2.2.3
  flutter_html: ^3.0.0-beta.2
  flutter_launcher_icons: ^0.14.1
  flutter_local_notifications: ^18.0.1
  flutter_lucide: ^1.5.0
  flutter_screenutil: ^5.9.3
  flutter_secure_storage: ^9.2.2
  flutter_staggered_animations: ^1.1.1
  flutter_svg: ^2.0.14
  flutter_timer_countdown: ^1.0.7
  # flutter_widget_from_html: ^0.15.3
  fluttertoast: ^8.2.8
  freezed_annotation: ^2.4.4
  get_it: ^7.7.0
  hydrated_bloc: ^9.1.5
  image_cropper: ^8.0.2
  image_picker: ^1.1.2
  injectable: ^2.4.4
  intl: ^0.19.0
  k_chart_plus:
    path: packages/k_chart_plus-1.0.2
  package_info_plus: ^8.1.1
  path_provider: ^2.1.5
  pinput: ^5.0.0
  qr_flutter: ^4.1.0
  readmore: ^3.0.0
  sf_cli: ^1.0.3
  shared_preferences: ^2.3.3
  shimmer_animation: ^2.2.1
  slide_countdown: ^2.0.0
  timezone: ^0.9.2
  url_launcher: ^6.3.1
  video_player: ^2.9.2
  web_socket_channel: ^2.4.0
  webview_flutter: any
  html: ^0.15.0
  draggable_float_widget: ^0.1.0
  flutter_widget_from_html: ^0.16.0

  
dependency_overrides:
  uuid: ^4.4.2
  file: ^6.1.2
  json_annotation: ^4.9.0
  wakelock_plus: 1.2.11

dev_dependencies:
  build_runner: ^2.4.13
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter
  freezed: ^2.5.7
  injectable_generator: ^2.6.2
  json_serializable: ^6.9.0

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/logo/sf_app/logo.png"
  adaptive_icon_background: "assets/logo/app-logo-bg.png"
  adaptive_icon_foreground: "assets/logo/sf_app/logo.png"
  min_sdk_android: 21

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/images/
    - assets/icons/
    - assets/svg/
    - assets/svg/sis/
    - assets/html/
    - assets/translations/
    - assets/flags/
    - assets/logo/
[1;33m⚠️ Excluding: assets/logo/ncm/[0m
[1;33m⚠️ Excluding: assets/logo/cfroex/[0m
[1;33m⚠️ Excluding: assets/logo/sf_app/[0m
    - assets/logo/sis/
[1;33m⚠️ Excluding: assets/splash/ncm/[0m
[1;33m⚠️ Excluding: assets/splash/cfroex/[0m
[1;33m⚠️ Excluding: assets/splash/sf_app/[0m
    - assets/splash/sis/



  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Bold.ttf
        - asset: assets/fonts/Poppins-ExtraLight.ttf
        - asset: assets/fonts/Poppins-Light.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
        - asset: assets/fonts/Poppins-Regular.ttf
